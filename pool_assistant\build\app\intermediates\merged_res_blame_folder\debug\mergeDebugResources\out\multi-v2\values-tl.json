{"logs": [{"outputFile": "com.poolassistant.pool_assistant.app-mergeDebugResources-50:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,904,995,1087,1182,1276,1377,1470,1565,1659,1750,1841,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,77,90,91,94,93,100,92,94,93,90,90,82,108,109,100,109,117,107,162,101,83", "endOffsets": "211,319,432,520,626,741,821,899,990,1082,1177,1271,1372,1465,1560,1654,1745,1836,1919,2028,2138,2239,2349,2467,2575,2738,2840,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,904,995,1087,1182,1276,1377,1470,1565,1659,1750,1841,1924,2033,2143,2244,2354,2472,2580,2743,9259", "endColumns": "110,107,112,87,105,114,79,77,90,91,94,93,100,92,94,93,90,90,82,108,109,100,109,117,107,162,101,83", "endOffsets": "211,319,432,520,626,741,821,899,990,1082,1177,1271,1372,1465,1560,1654,1745,1836,1919,2028,2138,2239,2349,2467,2575,2738,2840,9338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,264,346,485,654,739", "endColumns": "71,86,81,138,168,84,80", "endOffsets": "172,259,341,480,649,734,815"}, "to": {"startLines": "76,78,82,83,86,87,88", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8455,8630,9038,9120,9444,9613,9698", "endColumns": "71,86,81,138,168,84,80", "endOffsets": "8522,8712,9115,9254,9608,9693,9774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "77,79,80,81", "startColumns": "4,4,4,4", "startOffsets": "8527,8717,8822,8933", "endColumns": "102,104,110,104", "endOffsets": "8625,8817,8928,9033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e820d61ce37e44c44b5b21160e7f3c12\\transformed\\jetified-facebook-login-16.3.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,373,536,623,710,790,881,974,1095,1205,1300,1395,1501,1625,1708,1792,1985,2078,2180,2298,2412", "endColumns": "173,143,162,86,86,79,90,92,120,109,94,94,105,123,82,83,192,92,101,117,113,156", "endOffsets": "224,368,531,618,705,785,876,969,1090,1200,1295,1390,1496,1620,1703,1787,1980,2073,2175,2293,2407,2564"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3579,3753,3897,4060,4147,4234,4314,4405,4498,4619,4729,4824,4919,5025,5149,5232,5316,5509,5602,5704,5822,5936", "endColumns": "173,143,162,86,86,79,90,92,120,109,94,94,105,123,82,83,192,92,101,117,113,156", "endOffsets": "3748,3892,4055,4142,4229,4309,4400,4493,4614,4724,4819,4914,5020,5144,5227,5311,5504,5597,5699,5817,5931,6088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d4003e89e11d20e7aac223715520d9d9\\transformed\\jetified-play-services-base-18.0.1\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "58,59,60,61,62,63,64,65,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6093,6200,6376,6514,6623,6781,6917,7039,7297,7476,7583,7761,7899,8061,8240,8308,8374", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "6195,6371,6509,6618,6776,6912,7034,7147,7471,7578,7756,7894,8056,8235,8303,8369,8450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\21b502c8435235a3b0ddf73178b4adb9\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "66", "startColumns": "4", "startOffsets": "7152", "endColumns": "144", "endOffsets": "7292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "29,30,31,32,33,34,35,85", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2845,2942,3044,3145,3242,3349,3457,9343", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "2937,3039,3140,3237,3344,3452,3574,9439"}}]}]}