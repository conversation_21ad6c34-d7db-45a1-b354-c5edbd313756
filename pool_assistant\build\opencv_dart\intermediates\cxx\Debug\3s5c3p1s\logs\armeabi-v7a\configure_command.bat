@echo off
"C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HC:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=armeabi-v7a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a" ^
  "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620" ^
  "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\pool\\pool_assistant\\build\\opencv_dart\\intermediates\\cxx\\Debug\\3s5c3p1s\\obj\\armeabi-v7a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\pool\\pool_assistant\\build\\opencv_dart\\intermediates\\cxx\\Debug\\3s5c3p1s\\obj\\armeabi-v7a" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-BC:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx\\Debug\\3s5c3p1s\\armeabi-v7a" ^
  -GNinja ^
  "-DANDROID_ARM_NEON=TRUE" ^
  "-DANDROID_STL=c++_static" ^
  "-DCMAKE_INSTALL_PREFIX=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android/src/main/jniLibs"
