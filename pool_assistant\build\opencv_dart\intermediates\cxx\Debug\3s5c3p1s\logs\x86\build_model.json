{"info": {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, "cxxBuildFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx\\Debug\\3s5c3p1s\\x86", "soFolder": "C:\\Users\\<USER>\\Desktop\\pool\\pool_assistant\\build\\opencv_dart\\intermediates\\cxx\\Debug\\3s5c3p1s\\obj\\x86", "soRepublishFolder": "C:\\Users\\<USER>\\Desktop\\pool\\pool_assistant\\build\\opencv_dart\\intermediates\\cmake\\debug\\obj\\x86", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_ARM_NEON=TRUE", "-DANDROID_STL=c++_static", "-DCMAKE_INSTALL_PREFIX=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android/src/main/jniLibs"], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx", "intermediatesBaseFolder": "C:\\Users\\<USER>\\Desktop\\pool\\pool_assistant\\build\\opencv_dart\\intermediates", "intermediatesFolder": "C:\\Users\\<USER>\\Desktop\\pool\\pool_assistant\\build\\opencv_dart\\intermediates\\cxx", "gradleModulePathName": ":opencv_dart", "moduleRootFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android", "moduleBuildFile": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\build.gradle", "makeFile": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620", "ndkFolderBeforeSymLinking": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620", "ndkVersion": "23.1.7779620", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 16, "max": 31, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "C:\\Users\\<USER>\\Desktop\\pool\\pool_assistant\\android", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx\\Debug\\3s5c3p1s\\prefab\\x86", "isActiveAbi": true, "fullConfigurationHash": "3s5c3p1s256c55121r2z64485w143n6j4z4c015432jf5l5101qe3he471l", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.7.0.\n#   - $NDK is the path to NDK 23.1.7779620.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HC:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/src\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DC<PERSON>KE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-<PERSON><PERSON><PERSON>_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:/Users/<USER>/Desktop/pool/pool_assistant/build/opencv_dart/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:/Users/<USER>/Desktop/pool/pool_assistant/build/opencv_dart/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-BC:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DANDROID_ARM_NEON=TRUE\n-DANDROID_STL=c++_static\n-DCMAKE_INSTALL_PREFIX=C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/src/main/jniLibs", "configurationArguments": ["-HC:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=x86", "-DCMAKE_ANDROID_ARCH_ABI=x86", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\pool\\pool_assistant\\build\\opencv_dart\\intermediates\\cxx\\Debug\\3s5c3p1s\\obj\\x86", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\pool\\pool_assistant\\build\\opencv_dart\\intermediates\\cxx\\Debug\\3s5c3p1s\\obj\\x86", "-DCMAKE_BUILD_TYPE=Debug", "-BC:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx\\Debug\\3s5c3p1s\\x86", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_ARM_NEON=TRUE", "-DANDROID_STL=c++_static", "-DCMAKE_INSTALL_PREFIX=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android/src/main/jniLibs"], "intermediatesParentFolder": "C:\\Users\\<USER>\\Desktop\\pool\\pool_assistant\\build\\opencv_dart\\intermediates\\cxx\\Debug\\3s5c3p1s"}