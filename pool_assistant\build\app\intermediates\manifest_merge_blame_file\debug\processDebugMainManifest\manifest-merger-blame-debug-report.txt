1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.poolassistant.pool_assistant"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:5:5-67
15-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:5:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:6:5-79
16-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:6:22-76
17    <uses-permission
17-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:5-107
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:22-77
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:78-104
20    <uses-permission
20-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:5-108
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:22-78
22        android:maxSdkVersion="32" />
22-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:79-105
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:9:5-76
23-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:9:22-73
24    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
24-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:10:5-78
24-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:10:22-75
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
25-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:11:5-77
25-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:11:22-74
26    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- For devices with Android 11 (API level 30) and higher -->
26-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:12:5-83
26-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:12:22-80
27    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
27-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:15:5-16:53
27-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:15:22-74
28    <!--
29 Required to query activities that can process text, see:
30         https://developer.android.com/training/package-visibility and
31         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
32
33         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
34    -->
35    <queries>
35-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:58:5-63:15
36        <intent>
36-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:59:9-62:18
37            <action android:name="android.intent.action.PROCESS_TEXT" />
37-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:60:13-72
37-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:60:21-70
38
39            <data android:mimeType="text/plain" />
39-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:61:13-50
39-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:61:19-48
40        </intent>
41
42        <package android:name="com.facebook.katana" />
42-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:18:9-55
42-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:18:18-52
43    </queries>
44
45    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
45-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:16:5-79
45-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:16:22-76
46
47    <permission
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.poolassistant.pool_assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.poolassistant.pool_assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
52-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff3ef75755850822f83c0a36c594cb7\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
52-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff3ef75755850822f83c0a36c594cb7\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
53
54    <application
55        android:name="android.app.Application"
56        android:allowBackup="true"
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
58        android:debuggable="true"
59        android:extractNativeLibs="false"
60        android:fullBackupContent="true"
61        android:icon="@mipmap/ic_launcher"
62        android:label="Dark Pool"
63        android:requestLegacyExternalStorage="true"
64        android:supportsRtl="true" >
64-->[com.facebook.android:facebook-login:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e820d61ce37e44c44b5b21160e7f3c12\transformed\jetified-facebook-login-16.3.0\AndroidManifest.xml:16:18-44
65        <activity
66            android:name="com.poolassistant.pool_assistant.MainActivity"
67            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
68            android:exported="true"
69            android:hardwareAccelerated="true"
70            android:launchMode="singleTop"
71            android:taskAffinity=""
72            android:theme="@style/LaunchTheme"
73            android:windowSoftInputMode="adjustResize" >
74
75            <!--
76                 Specifies an Android theme to apply to this Activity as soon as
77                 the Android process has started. This theme is visible to the user
78                 while the Flutter UI initializes. After that, this theme continues
79                 to determine the Window background behind the Flutter UI.
80            -->
81            <meta-data
82                android:name="io.flutter.embedding.android.NormalTheme"
83                android:resource="@style/NormalTheme" />
84
85            <intent-filter>
86                <action android:name="android.intent.action.MAIN" />
87
88                <category android:name="android.intent.category.LAUNCHER" />
89            </intent-filter>
90        </activity>
91        <!--
92             Don't delete the meta-data below.
93             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
94        -->
95        <meta-data
96            android:name="flutterEmbedding"
97            android:value="2" />
98
99        <activity
99-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
100            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
100-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
101            android:exported="false"
101-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
102            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
102-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
103        <activity
103-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:22:9-25:66
104            android:name="com.facebook.FacebookActivity"
104-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:23:13-57
105            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
105-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:24:13-96
106            android:theme="@style/com_facebook_activity_theme" />
106-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:25:13-63
107        <activity android:name="com.facebook.CustomTabMainActivity" />
107-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:26:9-71
107-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:26:19-68
108        <activity
108-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:27:9-41:20
109            android:name="com.facebook.CustomTabActivity"
109-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:28:13-58
110            android:exported="true" >
110-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:29:13-36
111            <intent-filter>
111-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:31:13-40:29
112                <action android:name="android.intent.action.VIEW" />
112-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:32:17-69
112-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:32:25-66
113
114                <category android:name="android.intent.category.DEFAULT" />
114-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:34:17-76
114-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:34:27-73
115                <category android:name="android.intent.category.BROWSABLE" />
115-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:35:17-78
115-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:35:27-75
116
117                <data
117-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:61:13-50
118                    android:host="cct.com.poolassistant.pool_assistant"
119                    android:scheme="fbconnect" />
120            </intent-filter>
121        </activity>
122
123        <uses-library
123-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
124            android:name="androidx.window.extensions"
124-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
125            android:required="false" />
125-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
126        <uses-library
126-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
127            android:name="androidx.window.sidecar"
127-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
128            android:required="false" />
128-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
129
130        <activity
130-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
131            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
131-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
132            android:excludeFromRecents="true"
132-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
133            android:exported="false"
133-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
134            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
134-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
135        <!--
136            Service handling Google Sign-In user revocation. For apps that do not integrate with
137            Google Sign-In, this service will never be started.
138        -->
139        <service
139-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
140            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
140-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
141            android:exported="true"
141-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
142            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
142-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
143            android:visibleToInstantApps="true" />
143-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
144
145        <activity
145-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
146            android:name="com.google.android.gms.common.api.GoogleApiActivity"
146-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
147            android:exported="false"
147-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
148            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
148-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
149
150        <meta-data
150-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21b502c8435235a3b0ddf73178b4adb9\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
151            android:name="com.google.android.gms.version"
151-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21b502c8435235a3b0ddf73178b4adb9\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
152            android:value="@integer/google_play_services_version" />
152-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21b502c8435235a3b0ddf73178b4adb9\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
153        <!--
154         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
155         with the application context. This config is merged in with the host app's manifest,
156         but there can only be one provider with the same authority activated at any given
157         point; so if the end user has two or more different apps that use Facebook SDK, only the
158         first one will be able to use the provider. To work around this problem, we use the
159         following placeholder in the authority to identify each host application as if it was
160         a completely different provider.
161        -->
162        <provider
162-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:29:9-32:40
163            android:name="com.facebook.internal.FacebookInitProvider"
163-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:30:13-70
164            android:authorities="com.poolassistant.pool_assistant.FacebookInitProvider"
164-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:31:13-72
165            android:exported="false" />
165-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:32:13-37
166
167        <receiver
167-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:34:9-40:20
168            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
168-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:35:13-86
169            android:exported="false" >
169-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:36:13-37
170            <intent-filter>
170-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:37:13-39:29
171                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
171-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:38:17-95
171-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:38:25-92
172            </intent-filter>
173        </receiver>
174        <receiver
174-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:41:9-47:20
175            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
175-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:42:13-118
176            android:exported="false" >
176-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:43:13-37
177            <intent-filter>
177-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:44:13-46:29
178                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
178-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:45:17-103
178-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:45:25-100
179            </intent-filter>
180        </receiver>
181
182        <provider
182-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
183            android:name="androidx.startup.InitializationProvider"
183-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
184            android:authorities="com.poolassistant.pool_assistant.androidx-startup"
184-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
185            android:exported="false" >
185-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
186            <meta-data
186-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
187                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
187-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
188                android:value="androidx.startup" />
188-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
189            <meta-data
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
190                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
191                android:value="androidx.startup" />
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
192        </provider>
193
194        <receiver
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
195            android:name="androidx.profileinstaller.ProfileInstallReceiver"
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
196            android:directBootAware="false"
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
197            android:enabled="true"
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
198            android:exported="true"
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
199            android:permission="android.permission.DUMP" >
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
200            <intent-filter>
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
201                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
202            </intent-filter>
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
204                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
205            </intent-filter>
206            <intent-filter>
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
207                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
208            </intent-filter>
209            <intent-filter>
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
210                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
211            </intent-filter>
212        </receiver>
213    </application>
214
215</manifest>
