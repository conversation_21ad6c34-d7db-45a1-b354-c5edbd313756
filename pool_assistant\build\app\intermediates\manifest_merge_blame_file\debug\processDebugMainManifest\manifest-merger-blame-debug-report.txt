1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.poolassistant.pool_assistant"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:5:5-67
15-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:5:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:6:5-79
16-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:6:22-76
17    <uses-permission
17-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:5-107
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:22-77
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:78-104
20    <uses-permission
20-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:5-108
21        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
21-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:22-78
22        android:maxSdkVersion="32" />
22-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:79-105
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:9:5-76
23-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:9:22-73
24    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
24-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:10:5-78
24-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:10:22-75
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
25-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:11:5-77
25-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:11:22-74
26    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- Screen capture and overlay permissions -->
26-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:12:5-83
26-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:12:22-80
27    <uses-permission android:name="android.permission.RECORD_AUDIO" />
27-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:15:5-71
27-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:15:22-68
28    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
28-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:16:5-79
28-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:16:22-76
29    <uses-permission android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT" />
29-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:17:5-86
29-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:17:22-83
30    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" /> <!-- Camera permission for computer vision (if needed) -->
30-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:18:5-80
30-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:18:22-77
31    <uses-permission android:name="android.permission.CAMERA" /> <!-- Hardware features -->
31-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:21:5-65
31-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:21:22-62
32    <uses-feature
32-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:24:5-85
33        android:name="android.hardware.camera"
33-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:24:19-57
34        android:required="false" />
34-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:24:58-82
35    <uses-feature
35-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:25:5-95
36        android:name="android.hardware.camera.autofocus"
36-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:25:19-67
37        android:required="false" /> <!-- For devices with Android 11 (API level 30) and higher -->
37-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:25:68-92
38    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
38-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:28:5-29:53
38-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:28:22-74
39    <!--
40 Required to query activities that can process text, see:
41         https://developer.android.com/training/package-visibility and
42         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
43
44         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
45    -->
46    <queries>
46-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:79:5-84:15
47        <intent>
47-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:80:9-83:18
48            <action android:name="android.intent.action.PROCESS_TEXT" />
48-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:81:13-72
48-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:81:21-70
49
50            <data android:mimeType="text/plain" />
50-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:82:13-50
50-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:82:19-48
51        </intent>
52
53        <package android:name="com.facebook.katana" />
53-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:18:9-55
53-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:18:18-52
54    </queries>
55
56    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
56-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:16:5-79
56-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:16:22-76
57
58    <permission
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
59        android:name="com.poolassistant.pool_assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
60        android:protectionLevel="signature" />
60-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
61
62    <uses-permission android:name="com.poolassistant.pool_assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
62-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
62-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
63    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
63-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff3ef75755850822f83c0a36c594cb7\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
63-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff3ef75755850822f83c0a36c594cb7\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
64
65    <application
66        android:name="android.app.Application"
67        android:allowBackup="true"
68        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
68-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
69        android:debuggable="true"
70        android:extractNativeLibs="false"
71        android:fullBackupContent="true"
72        android:icon="@mipmap/ic_launcher"
73        android:label="Dark Pool"
74        android:requestLegacyExternalStorage="true"
75        android:supportsRtl="true" >
75-->[com.facebook.android:facebook-login:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e820d61ce37e44c44b5b21160e7f3c12\transformed\jetified-facebook-login-16.3.0\AndroidManifest.xml:16:18-44
76        <activity
77            android:name="com.poolassistant.pool_assistant.MainActivity"
78            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
79            android:exported="true"
80            android:hardwareAccelerated="true"
81            android:launchMode="singleTop"
82            android:taskAffinity=""
83            android:theme="@style/LaunchTheme"
84            android:windowSoftInputMode="adjustResize" >
85
86            <!--
87                 Specifies an Android theme to apply to this Activity as soon as
88                 the Android process has started. This theme is visible to the user
89                 while the Flutter UI initializes. After that, this theme continues
90                 to determine the Window background behind the Flutter UI.
91            -->
92            <meta-data
93                android:name="io.flutter.embedding.android.NormalTheme"
94                android:resource="@style/NormalTheme" />
95
96            <intent-filter>
97                <action android:name="android.intent.action.MAIN" />
98
99                <category android:name="android.intent.category.LAUNCHER" />
100            </intent-filter>
101        </activity>
102
103        <!-- Overlay Service for drawing aim lines -->
104        <service
105            android:name="com.poolassistant.pool_assistant.OverlayService"
106            android:enabled="true"
107            android:exported="false"
108            android:foregroundServiceType="mediaProjection" />
109
110        <!--
111             Don't delete the meta-data below.
112             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
113        -->
114        <meta-data
115            android:name="flutterEmbedding"
116            android:value="2" />
117
118        <activity
118-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
119            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
119-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
120            android:exported="false"
120-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
121            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
121-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
122
123        <service
123-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
124            android:name="androidx.camera.core.impl.MetadataHolderService"
124-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
125            android:enabled="false"
125-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
126            android:exported="false" >
126-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
127            <meta-data
127-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
128                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
128-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
129                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
129-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
130        </service>
131
132        <activity
132-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:22:9-25:66
133            android:name="com.facebook.FacebookActivity"
133-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:23:13-57
134            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
134-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:24:13-96
135            android:theme="@style/com_facebook_activity_theme" />
135-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:25:13-63
136        <activity android:name="com.facebook.CustomTabMainActivity" />
136-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:26:9-71
136-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:26:19-68
137        <activity
137-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:27:9-41:20
138            android:name="com.facebook.CustomTabActivity"
138-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:28:13-58
139            android:exported="true" >
139-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:29:13-36
140            <intent-filter>
140-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:31:13-40:29
141                <action android:name="android.intent.action.VIEW" />
141-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:32:17-69
141-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:32:25-66
142
143                <category android:name="android.intent.category.DEFAULT" />
143-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:34:17-76
143-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:34:27-73
144                <category android:name="android.intent.category.BROWSABLE" />
144-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:35:17-78
144-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:35:27-75
145
146                <data
146-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:82:13-50
147                    android:host="cct.com.poolassistant.pool_assistant"
148                    android:scheme="fbconnect" />
149            </intent-filter>
150        </activity>
151
152        <uses-library
152-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
153            android:name="androidx.window.extensions"
153-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
154            android:required="false" />
154-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
155        <uses-library
155-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
156            android:name="androidx.window.sidecar"
156-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
157            android:required="false" />
157-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
158
159        <activity
159-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
160            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
160-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
161            android:excludeFromRecents="true"
161-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
162            android:exported="false"
162-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
163            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
163-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
164        <!--
165            Service handling Google Sign-In user revocation. For apps that do not integrate with
166            Google Sign-In, this service will never be started.
167        -->
168        <service
168-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
169            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
169-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
170            android:exported="true"
170-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
171            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
171-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
172            android:visibleToInstantApps="true" />
172-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
173
174        <activity
174-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
175            android:name="com.google.android.gms.common.api.GoogleApiActivity"
175-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
176            android:exported="false"
176-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
177            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
177-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
178
179        <meta-data
179-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21b502c8435235a3b0ddf73178b4adb9\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
180            android:name="com.google.android.gms.version"
180-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21b502c8435235a3b0ddf73178b4adb9\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
181            android:value="@integer/google_play_services_version" />
181-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21b502c8435235a3b0ddf73178b4adb9\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
182        <!--
183         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
184         with the application context. This config is merged in with the host app's manifest,
185         but there can only be one provider with the same authority activated at any given
186         point; so if the end user has two or more different apps that use Facebook SDK, only the
187         first one will be able to use the provider. To work around this problem, we use the
188         following placeholder in the authority to identify each host application as if it was
189         a completely different provider.
190        -->
191        <provider
191-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:29:9-32:40
192            android:name="com.facebook.internal.FacebookInitProvider"
192-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:30:13-70
193            android:authorities="com.poolassistant.pool_assistant.FacebookInitProvider"
193-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:31:13-72
194            android:exported="false" />
194-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:32:13-37
195
196        <receiver
196-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:34:9-40:20
197            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
197-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:35:13-86
198            android:exported="false" >
198-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:36:13-37
199            <intent-filter>
199-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:37:13-39:29
200                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
200-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:38:17-95
200-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:38:25-92
201            </intent-filter>
202        </receiver>
203        <receiver
203-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:41:9-47:20
204            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
204-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:42:13-118
205            android:exported="false" >
205-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:43:13-37
206            <intent-filter>
206-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:44:13-46:29
207                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
207-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:45:17-103
207-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:45:25-100
208            </intent-filter>
209        </receiver>
210
211        <provider
211-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
212            android:name="androidx.startup.InitializationProvider"
212-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
213            android:authorities="com.poolassistant.pool_assistant.androidx-startup"
213-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
214            android:exported="false" >
214-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
215            <meta-data
215-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
216                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
216-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
217                android:value="androidx.startup" />
217-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
218            <meta-data
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
219                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
220                android:value="androidx.startup" />
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
221        </provider>
222
223        <receiver
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
224            android:name="androidx.profileinstaller.ProfileInstallReceiver"
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
225            android:directBootAware="false"
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
226            android:enabled="true"
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
227            android:exported="true"
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
228            android:permission="android.permission.DUMP" >
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
229            <intent-filter>
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
230                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
231            </intent-filter>
232            <intent-filter>
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
233                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
234            </intent-filter>
235            <intent-filter>
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
236                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
237            </intent-filter>
238            <intent-filter>
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
239                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
240            </intent-filter>
241        </receiver>
242    </application>
243
244</manifest>
