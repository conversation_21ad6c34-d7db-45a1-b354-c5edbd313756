[{"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_checkbox_to_unchecked_icon_null_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_checkbox_to_unchecked_icon_null_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_slide_out_bottom.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_slide_out_bottom.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_slide_out_top.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_slide_out_top.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_checkbox_to_unchecked_check_path_merged_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_checkbox_to_unchecked_check_path_merged_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_radio_to_off_mtrl_dot_group_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_radio_to_off_mtrl_dot_group_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_checkbox_to_checked_box_inner_merged_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_checkbox_to_checked_box_inner_merged_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_fade_out.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_fade_out.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_tooltip_exit.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_tooltip_exit.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_checkbox_to_unchecked_box_inner_merged_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_checkbox_to_unchecked_box_inner_merged_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_slide_in_bottom.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_slide_in_bottom.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_radio_to_on_mtrl_ring_outer_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_radio_to_on_mtrl_ring_outer_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_tooltip_enter.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_tooltip_enter.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_checkbox_to_checked_box_outer_merged_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_checkbox_to_checked_box_outer_merged_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_checkbox_to_checked_icon_null_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_checkbox_to_checked_icon_null_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_popup_exit.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_popup_exit.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_radio_to_on_mtrl_ring_outer_path_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_radio_to_on_mtrl_ring_outer_path_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_grow_fade_in_from_bottom.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_grow_fade_in_from_bottom.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_radio_to_off_mtrl_ring_outer_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_radio_to_off_mtrl_ring_outer_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_slide_in_top.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_slide_in_top.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_radio_to_off_mtrl_ring_outer_path_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_radio_to_off_mtrl_ring_outer_path_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_fade_in.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_fade_in.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/btn_radio_to_on_mtrl_dot_group_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/btn_radio_to_on_mtrl_dot_group_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_shrink_fade_out_from_bottom.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_shrink_fade_out_from_bottom.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/anim/abc_popup_enter.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/anim/abc_popup_enter.xml"}]