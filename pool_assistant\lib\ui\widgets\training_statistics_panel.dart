import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Training statistics panel with performance tracking and analytics
class TrainingStatisticsPanel extends StatefulWidget {
  const TrainingStatisticsPanel({super.key});

  @override
  State<TrainingStatisticsPanel> createState() => _TrainingStatisticsPanelState();
}

class _TrainingStatisticsPanelState extends State<TrainingStatisticsPanel>
    with TickerProviderStateMixin {
  
  late AnimationController _chartController;
  late AnimationController _progressController;
  late Animation<double> _chartAnimation;
  late Animation<double> _progressAnimation;
  
  // Statistics data
  final TrainingStats _stats = TrainingStats.mock();
  
  // UI state
  StatsPeriod _selectedPeriod = StatsPeriod.week;
  StatsCategory _selectedCategory = StatsCategory.accuracy;

  @override
  void initState() {
    super.initState();
    
    _chartController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    
    _chartAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _chartController,
      curve: Curves.easeOutCubic,
    ));
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutBack,
    ));
    
    // Start animations
    _chartController.forward();
    _progressController.forward();
  }

  @override
  void dispose() {
    _chartController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1B263B).withValues(alpha: 0.95),
            const Color(0xFF0D1B2A).withValues(alpha: 0.95),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF9C27B0).withValues(alpha: 0.5),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF9C27B0).withValues(alpha: 0.2),
            blurRadius: 20,
            spreadRadius: 3,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 20),
          _buildPeriodSelector(),
          const SizedBox(height: 20),
          _buildOverviewCards(),
          const SizedBox(height: 20),
          _buildCategorySelector(),
          const SizedBox(height: 20),
          _buildPerformanceChart(),
          const SizedBox(height: 20),
          _buildProgressIndicators(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF9C27B0), Color(0xFF7B1FA2)],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.bar_chart,
            color: Colors.white,
            size: 28,
          ),
        ),
        const SizedBox(width: 16),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Training Statistics',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
              ),
              SizedBox(height: 4),
              Text(
                'Performance tracking & analytics',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: _exportStats,
          icon: const Icon(
            Icons.download,
            color: Colors.white,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildPeriodSelector() {
    return Row(
      children: StatsPeriod.values.map((period) {
        final isSelected = period == _selectedPeriod;
        return Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: GestureDetector(
              onTap: () => _selectPeriod(period),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  gradient: isSelected
                    ? const LinearGradient(
                        colors: [Color(0xFF9C27B0), Color(0xFF7B1FA2)],
                      )
                    : null,
                  color: isSelected ? null : Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected 
                      ? const Color(0xFF9C27B0)
                      : Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  _getPeriodName(period),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.white70,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildOverviewCards() {
    return Row(
      children: [
        Expanded(
          child: _buildOverviewCard(
            'Accuracy',
            '${(_stats.accuracy * 100).toInt()}%',
            Icons.center_focus_strong,
            const Color(0xFF4CAF50),
            _stats.accuracyTrend,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildOverviewCard(
            'Sessions',
            '${_stats.totalSessions}',
            Icons.schedule,
            const Color(0xFF2196F3),
            _stats.sessionsTrend,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildOverviewCard(
            'Avg Score',
            '${_stats.averageScore.toInt()}',
            Icons.star,
            const Color(0xFFFF9800),
            _stats.scoreTrend,
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewCard(
    String title,
    String value,
    IconData icon,
    Color color,
    double trend,
  ) {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _progressAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: color.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
                const SizedBox(height: 8),
                Text(
                  value,
                  style: TextStyle(
                    color: color,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      trend > 0 ? Icons.trending_up : Icons.trending_down,
                      color: trend > 0 ? const Color(0xFF4CAF50) : const Color(0xFFFF5722),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${trend.abs().toStringAsFixed(1)}%',
                      style: TextStyle(
                        color: trend > 0 ? const Color(0xFF4CAF50) : const Color(0xFFFF5722),
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategorySelector() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: StatsCategory.values.map((category) {
          final isSelected = category == _selectedCategory;
          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: GestureDetector(
              onTap: () => _selectCategory(category),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  gradient: isSelected
                    ? const LinearGradient(
                        colors: [Color(0xFF9C27B0), Color(0xFF7B1FA2)],
                      )
                    : null,
                  color: isSelected ? null : Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected 
                      ? const Color(0xFF9C27B0)
                      : Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  _getCategoryName(category),
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.white70,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPerformanceChart() {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF0D1B2A).withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF9C27B0).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getCategoryIcon(_selectedCategory),
                color: const Color(0xFF9C27B0),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '${_getCategoryName(_selectedCategory)} Over Time',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          Expanded(
            child: AnimatedBuilder(
              animation: _chartAnimation,
              builder: (context, child) {
                return CustomPaint(
                  painter: PerformanceChartPainter(
                    _stats.getDataForCategory(_selectedCategory),
                    _chartAnimation.value,
                  ),
                  size: Size.infinite,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicators() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(
              Icons.trending_up,
              color: Color(0xFF9C27B0),
              size: 20,
            ),
            SizedBox(width: 8),
            Text(
              'Skill Progress',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        _buildProgressBar('Aim Accuracy', _stats.aimAccuracy, const Color(0xFF4CAF50)),
        _buildProgressBar('Power Control', _stats.powerControl, const Color(0xFF2196F3)),
        _buildProgressBar('Spin Mastery', _stats.spinMastery, const Color(0xFFFF9800)),
        _buildProgressBar('Strategy', _stats.strategy, const Color(0xFF9C27B0)),
      ],
    );
  }

  Widget _buildProgressBar(String skill, double progress, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                skill,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${(progress * 100).toInt()}%',
                style: TextStyle(
                  color: color,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return Container(
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: progress * _progressAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [color, color.withValues(alpha: 0.7)],
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _getPeriodName(StatsPeriod period) {
    switch (period) {
      case StatsPeriod.day:
        return 'Today';
      case StatsPeriod.week:
        return 'Week';
      case StatsPeriod.month:
        return 'Month';
      case StatsPeriod.year:
        return 'Year';
    }
  }

  String _getCategoryName(StatsCategory category) {
    switch (category) {
      case StatsCategory.accuracy:
        return 'Accuracy';
      case StatsCategory.power:
        return 'Power';
      case StatsCategory.spin:
        return 'Spin';
      case StatsCategory.strategy:
        return 'Strategy';
    }
  }

  IconData _getCategoryIcon(StatsCategory category) {
    switch (category) {
      case StatsCategory.accuracy:
        return Icons.center_focus_strong;
      case StatsCategory.power:
        return Icons.flash_on;
      case StatsCategory.spin:
        return Icons.rotate_right;
      case StatsCategory.strategy:
        return Icons.psychology;
    }
  }

  void _selectPeriod(StatsPeriod period) {
    setState(() {
      _selectedPeriod = period;
    });
    _chartController.reset();
    _chartController.forward();
  }

  void _selectCategory(StatsCategory category) {
    setState(() {
      _selectedCategory = category;
    });
    _chartController.reset();
    _chartController.forward();
  }

  void _exportStats() {
    // Export statistics functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Statistics exported successfully!'),
        backgroundColor: Color(0xFF4CAF50),
      ),
    );
  }
}

// Data classes
enum StatsPeriod { day, week, month, year }
enum StatsCategory { accuracy, power, spin, strategy }

class TrainingStats {
  final double accuracy;
  final int totalSessions;
  final double averageScore;
  final double accuracyTrend;
  final double sessionsTrend;
  final double scoreTrend;
  final double aimAccuracy;
  final double powerControl;
  final double spinMastery;
  final double strategy;
  final Map<StatsCategory, List<double>> categoryData;

  TrainingStats({
    required this.accuracy,
    required this.totalSessions,
    required this.averageScore,
    required this.accuracyTrend,
    required this.sessionsTrend,
    required this.scoreTrend,
    required this.aimAccuracy,
    required this.powerControl,
    required this.spinMastery,
    required this.strategy,
    required this.categoryData,
  });

  factory TrainingStats.mock() {
    final random = math.Random();
    return TrainingStats(
      accuracy: 0.75 + random.nextDouble() * 0.2,
      totalSessions: 45 + random.nextInt(20),
      averageScore: 850 + random.nextDouble() * 150,
      accuracyTrend: (random.nextDouble() - 0.5) * 20,
      sessionsTrend: (random.nextDouble() - 0.3) * 15,
      scoreTrend: (random.nextDouble() - 0.4) * 25,
      aimAccuracy: 0.8 + random.nextDouble() * 0.15,
      powerControl: 0.7 + random.nextDouble() * 0.25,
      spinMastery: 0.6 + random.nextDouble() * 0.3,
      strategy: 0.75 + random.nextDouble() * 0.2,
      categoryData: {
        StatsCategory.accuracy: List.generate(10, (i) => 0.5 + random.nextDouble() * 0.4),
        StatsCategory.power: List.generate(10, (i) => 0.4 + random.nextDouble() * 0.5),
        StatsCategory.spin: List.generate(10, (i) => 0.3 + random.nextDouble() * 0.6),
        StatsCategory.strategy: List.generate(10, (i) => 0.6 + random.nextDouble() * 0.3),
      },
    );
  }

  List<double> getDataForCategory(StatsCategory category) {
    return categoryData[category] ?? [];
  }
}

/// Custom painter for performance chart
class PerformanceChartPainter extends CustomPainter {
  final List<double> data;
  final double animationValue;

  PerformanceChartPainter(this.data, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final paint = Paint()
      ..color = const Color(0xFF9C27B0)
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    final fillPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          const Color(0xFF9C27B0).withValues(alpha: 0.3),
          const Color(0xFF9C27B0).withValues(alpha: 0.1),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final path = Path();
    final fillPath = Path();

    final stepX = size.width / (data.length - 1);
    final animatedLength = (data.length * animationValue).round();

    if (animatedLength > 0) {
      final firstPoint = Offset(0, size.height - (data[0] * size.height));
      path.moveTo(firstPoint.dx, firstPoint.dy);
      fillPath.moveTo(firstPoint.dx, size.height);
      fillPath.lineTo(firstPoint.dx, firstPoint.dy);

      for (int i = 1; i < animatedLength; i++) {
        final point = Offset(i * stepX, size.height - (data[i] * size.height));
        path.lineTo(point.dx, point.dy);
        fillPath.lineTo(point.dx, point.dy);
      }

      fillPath.lineTo((animatedLength - 1) * stepX, size.height);
      fillPath.close();

      canvas.drawPath(fillPath, fillPaint);
      canvas.drawPath(path, paint);

      // Draw data points
      final pointPaint = Paint()
        ..color = const Color(0xFF9C27B0)
        ..style = PaintingStyle.fill;

      for (int i = 0; i < animatedLength; i++) {
        final point = Offset(i * stepX, size.height - (data[i] * size.height));
        canvas.drawCircle(point, 4.0, pointPaint);
      }
    }
  }

  @override
  bool shouldRepaint(PerformanceChartPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue || oldDelegate.data != data;
  }
}
