<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs"><file name="arm64-v8a/libavcodec.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\arm64-v8a\libavcodec.so"/><file name="arm64-v8a/libavfilter.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\arm64-v8a\libavfilter.so"/><file name="arm64-v8a/libavformat.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\arm64-v8a\libavformat.so"/><file name="arm64-v8a/libavutil.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\arm64-v8a\libavutil.so"/><file name="arm64-v8a/libswresample.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\arm64-v8a\libswresample.so"/><file name="arm64-v8a/libswscale.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\arm64-v8a\libswscale.so"/><file name="armeabi-v7a/libavcodec.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\armeabi-v7a\libavcodec.so"/><file name="armeabi-v7a/libavfilter.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\armeabi-v7a\libavfilter.so"/><file name="armeabi-v7a/libavformat.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\armeabi-v7a\libavformat.so"/><file name="armeabi-v7a/libavutil.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\armeabi-v7a\libavutil.so"/><file name="armeabi-v7a/libswresample.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\armeabi-v7a\libswresample.so"/><file name="armeabi-v7a/libswscale.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\armeabi-v7a\libswscale.so"/><file name="x86_64/libavcodec.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\x86_64\libavcodec.so"/><file name="x86_64/libavfilter.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\x86_64\libavfilter.so"/><file name="x86_64/libavformat.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\x86_64\libavformat.so"/><file name="x86_64/libavutil.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\x86_64\libavutil.so"/><file name="x86_64/libswresample.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\x86_64\libswresample.so"/><file name="x86_64/libswscale.so" path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\main\jniLibs\x86_64\libswscale.so"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\opencv_dart-1.4.1\android\src\debug\jniLibs"/></dataSet></merger>