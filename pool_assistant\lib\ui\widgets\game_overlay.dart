import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:vector_math/vector_math.dart';
import '../../models/ball.dart';
import '../../models/table.dart';
import '../../models/settings.dart';
import '../../physics/trajectory_predictor.dart';
import '../../services/game_detector.dart';
import '../../services/settings_service.dart';
import '../../services/overlay_renderer.dart';

/// Widget that renders the game overlay
class GameOverlay extends StatefulWidget {
  const GameOverlay({super.key});

  @override
  State<GameOverlay> createState() => _GameOverlayState();
}

class _GameOverlayState extends State<GameOverlay> with SingleTickerProviderStateMixin {
  /// Animation controller for smooth updates
  late AnimationController _animationController;

  /// Current pool table
  PoolTable? _table;

  /// Current shot direction
  Vector2? _shotDirection;

  /// Current shot power
  double _shotPower = 10.0;

  /// Trajectory predictor
  TrajectoryPredictor? _trajectoryPredictor;

  /// Predicted trajectories
  Map<int, List<Vector2>> _trajectories = {};

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 16), // ~60 FPS
    )..repeat();

    // Listen for game state updates
    final gameDetector = Provider.of<GameDetector>(context, listen: false);
    gameDetector.gameStateStream.listen(_onGameStateUpdated);

    // Initialize with current table if available
    if (gameDetector.currentTable != null) {
      _table = gameDetector.currentTable;
      _trajectoryPredictor = TrajectoryPredictor(table: _table!);
    }

    // Start updating shot direction and power
    _startUpdatingShotInfo();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Handles game state updates
  void _onGameStateUpdated(PoolTable table) {
    setState(() {
      _table = table;
      _trajectoryPredictor = TrajectoryPredictor(table: table);
      _updateTrajectories();
    });
  }

  /// Starts updating shot direction and power
  void _startUpdatingShotInfo() {
    // Update shot info every 100ms
    Future.doWhile(() async {
      await Future.delayed(const Duration(milliseconds: 100));

      if (!mounted) return false;

      final gameDetector = Provider.of<GameDetector>(context, listen: false);

      // Get shot direction
      final shotDirection = await gameDetector.getCurrentShotDirection();
      if (shotDirection != null &&
          (_shotDirection == null || (shotDirection - _shotDirection!).length > 0.01)) {
        setState(() {
          _shotDirection = shotDirection;
          _updateTrajectories();
        });
      }

      // Get shot power
      final shotPower = await gameDetector.getCurrentShotPower();
      if (shotPower != null && (_shotPower - shotPower).abs() > 0.1) {
        setState(() {
          _shotPower = shotPower;
          _updateTrajectories();
        });
      }

      return true;
    });
  }

  /// Updates the predicted trajectories
  void _updateTrajectories() {
    if (_table == null || _trajectoryPredictor == null || _shotDirection == null) {
      _trajectories = {};
      return;
    }

    final settings = Provider.of<SettingsService>(context, listen: false).settings;

    // Update trajectory predictor settings
    _trajectoryPredictor = TrajectoryPredictor(
      table: _table!,
      maxBounces: settings.maxBounces,
      maxSimulationTime: settings.enableFastMode ? 2.0 : 5.0,
      timeStep: settings.enableFastMode ? 0.02 : 0.01,
    );

    // Predict trajectories
    _trajectories = _trajectoryPredictor!.predictTrajectories(_shotDirection!, _shotPower);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsService>(
      builder: (context, settingsService, child) {
        final settings = settingsService.settings;

        // Don't render anything in streaming mode
        if (settings.enableStreamingMode) {
          return const SizedBox.shrink();
        }

        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return CustomPaint(
              painter: _OverlayPainter(
                table: _table,
                trajectories: _trajectories,
                settings: settings,
              ),
              size: Size.infinite,
            );
          },
        );
      },
    );
  }
}

/// Custom painter for rendering the overlay
class _OverlayPainter extends CustomPainter {
  /// The pool table to render
  final PoolTable? table;

  /// Map of ball IDs to their predicted trajectories
  final Map<int, List<Vector2>> trajectories;

  /// Application settings
  final AppSettings settings;

  _OverlayPainter({
    required this.table,
    required this.trajectories,
    required this.settings,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (table == null) return;

    final renderer = OverlayRenderer(
      table: table!,
      settings: settings,
      trajectories: trajectories,
      canvas: canvas,
      size: size,
    );

    renderer.render();
  }

  @override
  bool shouldRepaint(_OverlayPainter oldPainter) {
    return table != oldPainter.table ||
           trajectories != oldPainter.trajectories ||
           settings != oldPainter.settings;
  }
}
