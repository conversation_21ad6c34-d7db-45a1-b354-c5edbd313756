import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/aim_assistance_service.dart';
import '../../services/advanced_physics_service.dart';
import '../widgets/aim_assistance_control_panel.dart';
import '../widgets/advanced_physics_panel.dart';
import '../widgets/shot_analysis_panel.dart';
import '../widgets/training_statistics_panel.dart';

/// Advanced training screen with comprehensive aim assistance and analysis
class AdvancedTrainingScreen extends StatefulWidget {
  const AdvancedTrainingScreen({super.key});

  @override
  State<AdvancedTrainingScreen> createState() => _AdvancedTrainingScreenState();
}

class _AdvancedTrainingScreenState extends State<AdvancedTrainingScreen>
    with TickerProviderStateMixin {

  late TabController _tabController;
  late AnimationController _backgroundController;
  late Animation<double> _backgroundAnimation;

  // Services
  late AdvancedPhysicsService _physicsService;

  // UI State
  bool _isFullscreen = false;
  double _panelOpacity = 0.9;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 4, vsync: this);
    _physicsService = AdvancedPhysicsService();

    // Background animation
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.linear,
    ));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Animated background
          _buildAnimatedBackground(),

          // Main content
          SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(),

                // Tab bar
                _buildTabBar(),

                // Tab content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildAimAssistanceTab(),
                      _buildPhysicsAnalysisTab(),
                      _buildShotAnalysisTab(),
                      _buildStatisticsTab(),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Floating controls
          _buildFloatingControls(),
        ],
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF0D1B2A),
                const Color(0xFF1B263B),
                const Color(0xFF415A77),
                const Color(0xFF778DA9),
              ],
              stops: [
                0.0,
                0.3 + _backgroundAnimation.value * 0.1,
                0.6 + _backgroundAnimation.value * 0.1,
                1.0,
              ],
            ),
          ),
          child: CustomPaint(
            painter: NetworkPatternPainter(_backgroundAnimation.value),
            size: Size.infinite,
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Logo and title
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: const Icon(
              Icons.psychology,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Dark Pool Pro',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                  ),
                ),
                Text(
                  'Advanced Training Assistant',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Settings and fullscreen buttons
          IconButton(
            onPressed: () => _toggleFullscreen(),
            icon: Icon(
              _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
              color: Colors.white,
            ),
          ),
          IconButton(
            onPressed: () => _showSettings(),
            icon: const Icon(
              Icons.settings,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
          ),
          borderRadius: BorderRadius.circular(25),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white.withValues(alpha: 0.6),
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
        tabs: const [
          Tab(
            icon: Icon(Icons.center_focus_strong, size: 20),
            text: 'Aim Assist',
          ),
          Tab(
            icon: Icon(Icons.science, size: 20),
            text: 'Physics',
          ),
          Tab(
            icon: Icon(Icons.analytics, size: 20),
            text: 'Analysis',
          ),
          Tab(
            icon: Icon(Icons.bar_chart, size: 20),
            text: 'Stats',
          ),
        ],
      ),
    );
  }

  Widget _buildAimAssistanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Main control panel
          AnimatedOpacity(
            opacity: _panelOpacity,
            duration: const Duration(milliseconds: 300),
            child: const AimAssistanceControlPanel(),
          ),

          const SizedBox(height: 16),

          // Quick actions
          _buildQuickActions(),

          const SizedBox(height: 16),

          // Real-time feedback
          _buildRealTimeFeedback(),
        ],
      ),
    );
  }

  Widget _buildPhysicsAnalysisTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Physics control panel
          AnimatedOpacity(
            opacity: _panelOpacity,
            duration: const Duration(milliseconds: 300),
            child: AdvancedPhysicsPanel(physicsService: _physicsService),
          ),

          const SizedBox(height: 16),

          // Physics visualization
          _buildPhysicsVisualization(),
        ],
      ),
    );
  }

  Widget _buildShotAnalysisTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Shot analysis panel
          AnimatedOpacity(
            opacity: _panelOpacity,
            duration: const Duration(milliseconds: 300),
            child: const ShotAnalysisPanel(),
          ),

          const SizedBox(height: 16),

          // Shot recommendations
          _buildShotRecommendations(),
        ],
      ),
    );
  }

  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Statistics panel
          AnimatedOpacity(
            opacity: _panelOpacity,
            duration: const Duration(milliseconds: 300),
            child: const TrainingStatisticsPanel(),
          ),

          const SizedBox(height: 16),

          // Performance charts
          _buildPerformanceCharts(),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.black.withValues(alpha: 0.4),
            Colors.black.withValues(alpha: 0.2),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Actions',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  'Calibrate',
                  Icons.tune,
                  () => _calibrateSystem(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickActionButton(
                  'Reset',
                  Icons.refresh,
                  () => _resetSystem(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickActionButton(
                  'Export',
                  Icons.download,
                  () => _exportData(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton(String label, IconData icon, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF4CAF50).withValues(alpha: 0.2),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
            width: 1,
          ),
        ),
      ),
      icon: Icon(icon, size: 20),
      label: Text(
        label,
        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
      ),
    );
  }

  Widget _buildRealTimeFeedback() {
    return Consumer<AimAssistanceService>(
      builder: (context, aimService, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF1B263B).withValues(alpha: 0.8),
                const Color(0xFF0D1B2A).withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: aimService.isActive
                ? const Color(0xFF4CAF50).withValues(alpha: 0.5)
                : Colors.grey.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.speed,
                    color: aimService.isActive ? const Color(0xFF4CAF50) : Colors.grey,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Real-Time Feedback',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              if (aimService.isActive && aimService.currentPrediction != null) ...[
                _buildFeedbackMetric('Accuracy', '${(aimService.currentPrediction!.confidence * 100).toInt()}%'),
                _buildFeedbackMetric('Power', '${(aimService.currentPrediction!.power * 100).toInt()}%'),
                _buildFeedbackMetric('Angle', '${(aimService.currentPrediction!.angle * 180 / 3.14159).toInt()}°'),
              ] else ...[
                Text(
                  aimService.isActive ? 'Analyzing...' : 'Start aim assistance to see feedback',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildFeedbackMetric(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Color(0xFF4CAF50),
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhysicsVisualization() {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.black.withValues(alpha: 0.4),
            Colors.black.withValues(alpha: 0.2),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: const Center(
        child: Text(
          'Physics Visualization\n(3D Trajectory View)',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildShotRecommendations() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.black.withValues(alpha: 0.4),
            Colors.black.withValues(alpha: 0.2),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Shot Recommendations',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Text(
            'AI-powered shot suggestions will appear here',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceCharts() {
    return Container(
      height: 250,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.black.withValues(alpha: 0.4),
            Colors.black.withValues(alpha: 0.2),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: const Center(
        child: Text(
          'Performance Charts\n(Training Progress)',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingControls() {
    return Positioned(
      right: 16,
      top: MediaQuery.of(context).size.height * 0.3,
      child: Column(
        children: [
          FloatingActionButton(
            heroTag: "opacity",
            mini: true,
            backgroundColor: const Color(0xFF4CAF50),
            onPressed: () => _adjustOpacity(),
            child: const Icon(Icons.opacity, color: Colors.white),
          ),
          const SizedBox(height: 8),
          FloatingActionButton(
            heroTag: "help",
            mini: true,
            backgroundColor: const Color(0xFF2196F3),
            onPressed: () => _showHelp(),
            child: const Icon(Icons.help, color: Colors.white),
          ),
        ],
      ),
    );
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
  }

  void _adjustOpacity() {
    setState(() {
      _panelOpacity = _panelOpacity == 0.9 ? 0.6 : 0.9;
    });
  }

  void _showSettings() {
    // Show settings dialog
  }

  void _showHelp() {
    // Show help dialog
  }

  void _calibrateSystem() {
    // Calibrate the system
  }

  void _resetSystem() {
    // Reset the system
  }

  void _exportData() {
    // Export training data
  }
}

/// Custom painter for animated network pattern background
class NetworkPatternPainter extends CustomPainter {
  final double animationValue;

  NetworkPatternPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF4CAF50).withValues(alpha: 0.1)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw animated network pattern
    for (int i = 0; i < 10; i++) {
      for (int j = 0; j < 10; j++) {
        final x = (size.width / 10) * i;
        final y = (size.height / 10) * j;

        final offset = sin(animationValue * 2 * 3.14159 + i + j) * 10;

        canvas.drawCircle(
          Offset(x + offset, y + offset),
          2.0,
          paint,
        );

        if (i < 9) {
          canvas.drawLine(
            Offset(x + offset, y + offset),
            Offset(x + (size.width / 10) + offset, y + offset),
            paint,
          );
        }

        if (j < 9) {
          canvas.drawLine(
            Offset(x + offset, y + offset),
            Offset(x + offset, y + (size.height / 10) + offset),
            paint,
          );
        }
      }
    }
  }

  @override
  bool shouldRepaint(NetworkPatternPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}
