import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:vector_math/vector_math.dart';

/// Advanced physics service for realistic pool ball simulation
/// Includes spin, friction, air resistance, and complex collision dynamics
class AdvancedPhysicsService {
  // Physics constants
  static const double _ballMass = 0.17; // kg (standard pool ball)
  static const double _ballRadius = 0.028575; // meters (57.15mm diameter)
  static const double _tableLength = 2.84; // meters (9 feet)
  static const double _tableWidth = 1.42; // meters (4.5 feet)
  static const double _pocketRadius = 0.064; // meters

  // Friction coefficients
  static const double _rollingFriction = 0.01;
  static const double _slidingFriction = 0.2;
  static const double _spinFriction = 0.05;
  static const double _airResistance = 0.001;

  // Collision parameters
  static const double _restitution = 0.95;
  static const double _cushionRestitution = 0.85;
  static const double _spinTransfer = 0.3;

  // Simulation parameters
  static const double _timeStep = 0.001; // 1ms steps for accuracy
  static const int _maxSteps = 10000;
  static const double _minVelocity = 0.01; // m/s

  /// Calculates advanced trajectory with spin, friction, and air resistance
  TrajectoryResult calculateAdvancedTrajectory({
    required Vector2 startPosition,
    required Vector2 initialVelocity,
    required Vector3 initialSpin, // x: backspin/topspin, y: sidespin, z: roll
    required List<Ball> obstacles,
    required TableGeometry table,
  }) {
    final trajectory = <TrajectoryPoint>[];
    final collisions = <CollisionEvent>[];

    var position = startPosition.clone();
    var velocity = initialVelocity.clone();
    var spin = initialSpin.clone();
    var time = 0.0;

    for (int step = 0; step < _maxSteps; step++) {
      time += _timeStep;

      // Add current state to trajectory
      trajectory.add(TrajectoryPoint(
        position: position.clone(),
        velocity: velocity.clone(),
        spin: spin.clone(),
        time: time,
      ));

      // Check if ball has stopped
      if (velocity.length < _minVelocity && spin.length < _minVelocity) {
        break;
      }

      // Calculate forces
      final forces = _calculateForces(velocity, spin);

      // Update velocity due to forces
      velocity += forces * _timeStep;

      // Calculate new position
      final newPosition = position + velocity * _timeStep;

      // Check for collisions
      final collision = _checkCollisions(position, newPosition, velocity, obstacles, table);

      if (collision != null) {
        // Handle collision
        position = collision.position;
        velocity = collision.newVelocity;
        spin = collision.newSpin;
        collisions.add(collision);

        // Add collision point to trajectory
        trajectory.add(TrajectoryPoint(
          position: position.clone(),
          velocity: velocity.clone(),
          spin: spin.clone(),
          time: time,
          isCollision: true,
        ));
      } else {
        // Normal movement
        position = newPosition;
      }

      // Update spin due to friction
      spin = _updateSpin(spin, velocity);
    }

    return TrajectoryResult(
      trajectory: trajectory,
      collisions: collisions,
      finalPosition: position,
      finalVelocity: velocity,
      finalSpin: spin,
      totalTime: time,
    );
  }

  /// Calculates optimal shot parameters for a given target
  ShotRecommendation calculateOptimalShot({
    required Ball cueBall,
    required Ball targetBall,
    required Vector2 targetPocket,
    required List<Ball> obstacles,
    required TableGeometry table,
    double maxPower = 10.0,
    bool allowSpin = true,
  }) {
    var bestShot = ShotRecommendation.empty();
    var bestScore = 0.0;

    // Try different power levels
    for (double power = 1.0; power <= maxPower; power += 0.5) {
      // Try different angles around the optimal angle
      final optimalAngle = _calculateOptimalAngle(cueBall.position, targetBall.position, targetPocket);

      for (double angleOffset = -0.2; angleOffset <= 0.2; angleOffset += 0.05) {
        final angle = optimalAngle + angleOffset;
        final velocity = Vector2(cos(angle), sin(angle)) * power;

        if (allowSpin) {
          // Try different spin combinations
          for (double topSpin = -2.0; topSpin <= 2.0; topSpin += 1.0) {
            for (double sideSpin = -1.0; sideSpin <= 1.0; sideSpin += 0.5) {
              final spin = Vector3(topSpin, sideSpin, 0.0);
              final shot = _evaluateShot(cueBall, velocity, spin, targetBall, targetPocket, obstacles, table);

              if (shot.score > bestScore) {
                bestScore = shot.score;
                bestShot = shot;
              }
            }
          }
        } else {
          // No spin shot
          final shot = _evaluateShot(cueBall, velocity, Vector3.zero(), targetBall, targetPocket, obstacles, table);

          if (shot.score > bestScore) {
            bestScore = shot.score;
            bestShot = shot;
          }
        }
      }
    }

    return bestShot;
  }

  /// Calculates forces acting on the ball
  Vector2 _calculateForces(Vector2 velocity, Vector3 spin) {
    var forces = Vector2.zero();

    // Rolling friction
    if (velocity.length > 0) {
      final frictionDirection = -velocity.normalized();
      final frictionMagnitude = _rollingFriction * _ballMass * 9.81; // mg
      forces += frictionDirection * frictionMagnitude;
    }

    // Air resistance
    if (velocity.length > 0) {
      final dragDirection = -velocity.normalized();
      final dragMagnitude = _airResistance * velocity.length2;
      forces += dragDirection * dragMagnitude;
    }

    // Magnus force (due to spin)
    if (spin.length > 0 && velocity.length > 0) {
      final magnusDirection = Vector2(-velocity.y, velocity.x).normalized();
      final magnusMagnitude = 0.1 * spin.y * velocity.length; // Simplified Magnus effect
      forces += magnusDirection * magnusMagnitude;
    }

    return forces / _ballMass; // F = ma, so a = F/m
  }

  /// Updates spin due to friction and rolling
  Vector3 _updateSpin(Vector3 spin, Vector2 velocity) {
    final newSpin = spin.clone();

    // Spin decay due to friction
    newSpin.x *= (1.0 - _spinFriction * _timeStep);
    newSpin.y *= (1.0 - _spinFriction * _timeStep);

    // Rolling motion (spin matches velocity)
    if (velocity.length > 0) {
      final rollSpin = velocity.length / _ballRadius;
      newSpin.z = rollSpin;
    } else {
      newSpin.z *= (1.0 - _spinFriction * _timeStep);
    }

    return newSpin;
  }

  /// Checks for collisions with obstacles and table boundaries
  CollisionEvent? _checkCollisions(
    Vector2 currentPos,
    Vector2 newPos,
    Vector2 velocity,
    List<Ball> obstacles,
    TableGeometry table,
  ) {
    // Check ball-to-ball collisions
    for (final obstacle in obstacles) {
      final collision = _checkBallCollision(currentPos, newPos, velocity, obstacle);
      if (collision != null) return collision;
    }

    // Check cushion collisions
    final cushionCollision = _checkCushionCollision(currentPos, newPos, velocity, table);
    if (cushionCollision != null) return cushionCollision;

    // Check pocket collisions
    final pocketCollision = _checkPocketCollision(newPos, table);
    if (pocketCollision != null) return pocketCollision;

    return null;
  }

  /// Checks collision with another ball
  CollisionEvent? _checkBallCollision(Vector2 currentPos, Vector2 newPos, Vector2 velocity, Ball obstacle) {
    final distance = (newPos - obstacle.position).length;
    final minDistance = _ballRadius * 2;

    if (distance <= minDistance) {
      // Calculate collision point
      final direction = (obstacle.position - currentPos).normalized();
      final collisionPoint = currentPos + direction * _ballRadius;

      // Calculate new velocities using elastic collision formulas
      final relativeVelocity = velocity;
      final collisionNormal = direction;

      final velocityAlongNormal = relativeVelocity.dot(collisionNormal);

      if (velocityAlongNormal > 0) return null; // Objects separating

      final impulse = 2 * velocityAlongNormal / 2; // Equal masses
      final newVelocity = velocity - collisionNormal * impulse * _restitution;

      return CollisionEvent(
        position: collisionPoint,
        newVelocity: newVelocity,
        newSpin: Vector3.zero(), // Simplified - no spin transfer
        type: CollisionType.ball,
        normal: collisionNormal,
      );
    }

    return null;
  }

  /// Checks collision with table cushions
  CollisionEvent? _checkCushionCollision(Vector2 currentPos, Vector2 newPos, Vector2 velocity, TableGeometry table) {
    final margin = _ballRadius;

    // Left cushion
    if (newPos.x <= table.bounds.left + margin) {
      return CollisionEvent(
        position: Vector2(table.bounds.left + margin, newPos.y),
        newVelocity: Vector2(-velocity.x * _cushionRestitution, velocity.y),
        newSpin: Vector3.zero(),
        type: CollisionType.cushion,
        normal: Vector2(1, 0),
      );
    }

    // Right cushion
    if (newPos.x >= table.bounds.right - margin) {
      return CollisionEvent(
        position: Vector2(table.bounds.right - margin, newPos.y),
        newVelocity: Vector2(-velocity.x * _cushionRestitution, velocity.y),
        newSpin: Vector3.zero(),
        type: CollisionType.cushion,
        normal: Vector2(-1, 0),
      );
    }

    // Top cushion
    if (newPos.y <= table.bounds.top + margin) {
      return CollisionEvent(
        position: Vector2(newPos.x, table.bounds.top + margin),
        newVelocity: Vector2(velocity.x, -velocity.y * _cushionRestitution),
        newSpin: Vector3.zero(),
        type: CollisionType.cushion,
        normal: Vector2(0, 1),
      );
    }

    // Bottom cushion
    if (newPos.y >= table.bounds.bottom - margin) {
      return CollisionEvent(
        position: Vector2(newPos.x, table.bounds.bottom - margin),
        newVelocity: Vector2(velocity.x, -velocity.y * _cushionRestitution),
        newSpin: Vector3.zero(),
        type: CollisionType.cushion,
        normal: Vector2(0, -1),
      );
    }

    return null;
  }

  /// Checks if ball falls into a pocket
  CollisionEvent? _checkPocketCollision(Vector2 position, TableGeometry table) {
    for (final pocket in table.pockets) {
      final distance = (position - pocket).length;
      if (distance <= _pocketRadius) {
        return CollisionEvent(
          position: pocket,
          newVelocity: Vector2.zero(),
          newSpin: Vector3.zero(),
          type: CollisionType.pocket,
          normal: Vector2.zero(),
        );
      }
    }
    return null;
  }

  /// Calculates optimal angle for hitting target ball into pocket
  double _calculateOptimalAngle(Vector2 cuePos, Vector2 targetPos, Vector2 pocketPos) {
    // Calculate the angle from target ball to pocket
    final targetToPocket = (pocketPos - targetPos).normalized();

    // Calculate the contact point on the target ball
    final contactPoint = targetPos - targetToPocket * _ballRadius * 2;

    // Calculate angle from cue ball to contact point
    final cueToContact = contactPoint - cuePos;
    return atan2(cueToContact.y, cueToContact.x);
  }

  /// Evaluates a shot and returns a score
  ShotRecommendation _evaluateShot(
    Ball cueBall,
    Vector2 velocity,
    Vector3 spin,
    Ball targetBall,
    Vector2 targetPocket,
    List<Ball> obstacles,
    TableGeometry table,
  ) {
    // Simulate the shot
    final trajectory = calculateAdvancedTrajectory(
      startPosition: cueBall.position,
      initialVelocity: velocity,
      initialSpin: spin,
      obstacles: obstacles,
      table: table,
    );

    // Calculate score based on various factors
    double score = 0.0;

    // Check if target ball is hit
    bool targetHit = false;
    for (final collision in trajectory.collisions) {
      if (collision.type == CollisionType.ball) {
        // Simplified - assume it's the target ball
        targetHit = true;
        score += 50.0;
        break;
      }
    }

    if (!targetHit) {
      return ShotRecommendation(
        velocity: velocity,
        spin: spin,
        trajectory: trajectory,
        score: 0.0,
        confidence: 0.0,
        difficulty: ShotDifficulty.impossible,
      );
    }

    // Calculate distance to pocket (closer is better)
    final finalDistance = (trajectory.finalPosition - targetPocket).length;
    score += max(0, 50 - finalDistance * 10);

    // Penalty for hitting other balls
    final ballCollisions = trajectory.collisions.where((c) => c.type == CollisionType.ball).length;
    score -= (ballCollisions - 1) * 10; // -1 because we want to hit the target

    // Penalty for cushion bounces (more bounces = harder shot)
    final cushionBounces = trajectory.collisions.where((c) => c.type == CollisionType.cushion).length;
    score -= cushionBounces * 5;

    // Calculate confidence and difficulty
    final confidence = (score / 100.0).clamp(0.0, 1.0);
    final difficulty = _calculateDifficulty(velocity.length, spin.length, cushionBounces, ballCollisions);

    return ShotRecommendation(
      velocity: velocity,
      spin: spin,
      trajectory: trajectory,
      score: score,
      confidence: confidence,
      difficulty: difficulty,
    );
  }

  ShotDifficulty _calculateDifficulty(double power, double spinAmount, int cushionBounces, int ballCollisions) {
    double difficultyScore = 0.0;

    // Power factor
    difficultyScore += power / 10.0;

    // Spin factor
    difficultyScore += spinAmount / 2.0;

    // Bounces factor
    difficultyScore += cushionBounces * 0.5;

    // Multiple ball collisions
    difficultyScore += max(0, ballCollisions - 1) * 1.0;

    if (difficultyScore < 1.0) return ShotDifficulty.easy;
    if (difficultyScore < 2.0) return ShotDifficulty.medium;
    if (difficultyScore < 3.0) return ShotDifficulty.hard;
    return ShotDifficulty.expert;
  }
}

// Data classes
class TrajectoryResult {
  final List<TrajectoryPoint> trajectory;
  final List<CollisionEvent> collisions;
  final Vector2 finalPosition;
  final Vector2 finalVelocity;
  final Vector3 finalSpin;
  final double totalTime;

  TrajectoryResult({
    required this.trajectory,
    required this.collisions,
    required this.finalPosition,
    required this.finalVelocity,
    required this.finalSpin,
    required this.totalTime,
  });
}

class TrajectoryPoint {
  final Vector2 position;
  final Vector2 velocity;
  final Vector3 spin;
  final double time;
  final bool isCollision;

  TrajectoryPoint({
    required this.position,
    required this.velocity,
    required this.spin,
    required this.time,
    this.isCollision = false,
  });
}

class CollisionEvent {
  final Vector2 position;
  final Vector2 newVelocity;
  final Vector3 newSpin;
  final CollisionType type;
  final Vector2 normal;

  CollisionEvent({
    required this.position,
    required this.newVelocity,
    required this.newSpin,
    required this.type,
    required this.normal,
  });
}

class ShotRecommendation {
  final Vector2 velocity;
  final Vector3 spin;
  final TrajectoryResult trajectory;
  final double score;
  final double confidence;
  final ShotDifficulty difficulty;

  ShotRecommendation({
    required this.velocity,
    required this.spin,
    required this.trajectory,
    required this.score,
    required this.confidence,
    required this.difficulty,
  });

  static ShotRecommendation empty() {
    return ShotRecommendation(
      velocity: Vector2.zero(),
      spin: Vector3.zero(),
      trajectory: TrajectoryResult(
        trajectory: [],
        collisions: [],
        finalPosition: Vector2.zero(),
        finalVelocity: Vector2.zero(),
        finalSpin: Vector3.zero(),
        totalTime: 0.0,
      ),
      score: 0.0,
      confidence: 0.0,
      difficulty: ShotDifficulty.impossible,
    );
  }
}

class Ball {
  final Vector2 position;
  final double radius;
  final String type;
  final String color;

  Ball({
    required this.position,
    required this.radius,
    required this.type,
    required this.color,
  });
}

class TableGeometry {
  final Rect bounds;
  final List<Vector2> pockets;

  TableGeometry({
    required this.bounds,
    required this.pockets,
  });
}

enum CollisionType { ball, cushion, pocket }
enum ShotDifficulty { easy, medium, hard, expert, impossible }
