[{"merged": "app.meedu.flutter_facebook_auth-release-33:/color-v23/abc_btn_colored_borderless_text_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/color-v23/abc_btn_colored_borderless_text_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/color-v23/abc_tint_default.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/color-v23/abc_tint_default.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/color-v23/abc_tint_btn_checkable.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/color-v23/abc_tint_btn_checkable.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/color-v23/abc_tint_spinner.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/color-v23/abc_tint_spinner.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/color-v23/abc_tint_edittext.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/color-v23/abc_tint_edittext.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/color-v23/abc_tint_seek_thumb.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/color-v23/abc_tint_seek_thumb.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/color-v23/abc_tint_switch_track.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/color-v23/abc_tint_switch_track.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/color-v23/abc_color_highlight_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/color-v23/abc_color_highlight_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/color-v23/abc_btn_colored_text_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/color-v23/abc_btn_colored_text_material.xml"}]