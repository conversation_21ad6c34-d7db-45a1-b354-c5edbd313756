#Sat May 24 11:16:41 PDT 2025
base.0=C\:\\Users\\Administrator\\Desktop\\pool\\pool_assistant\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=C\:\\Users\\Administrator\\Desktop\\pool\\pool_assistant\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\11\\classes.dex
base.2=C\:\\Users\\Administrator\\Desktop\\pool\\pool_assistant\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\12\\classes.dex
base.3=C\:\\Users\\Administrator\\Desktop\\pool\\pool_assistant\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\15\\classes.dex
base.4=C\:\\Users\\Administrator\\Desktop\\pool\\pool_assistant\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\5\\classes.dex
base.5=C\:\\Users\\Administrator\\Desktop\\pool\\pool_assistant\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\6\\classes.dex
base.6=C\:\\Users\\Administrator\\Desktop\\pool\\pool_assistant\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.7=C\:\\Users\\Administrator\\Desktop\\pool\\pool_assistant\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
base.8=C\:\\Users\\Administrator\\Desktop\\pool\\pool_assistant\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
base.9=C\:\\Users\\Administrator\\Desktop\\pool\\pool_assistant\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
path.0=classes.dex
path.1=11/classes.dex
path.2=12/classes.dex
path.3=15/classes.dex
path.4=5/classes.dex
path.5=6/classes.dex
path.6=0/classes.dex
path.7=1/classes.dex
path.8=8/classes.dex
path.9=classes2.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
