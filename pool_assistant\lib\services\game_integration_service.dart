import 'dart:io';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

/// Service for integrating and managing 8 Ball Pool game
class GameIntegrationService {
  static const String _gamePackageName = 'com.miniclip.eightballpool';
  static const String _gamePlayStoreUrl = 'https://play.google.com/store/apps/details?id=$_gamePackageName';
  static const String _gameApkUrl = 'https://apkpure.com/8-ball-pool/com.miniclip.eightballpool';
  
  /// Whether the game is installed
  bool _isGameInstalled = false;
  
  /// Whether the service is initialized
  bool _isInitialized = false;
  
  /// Game version information
  String? _gameVersion;
  
  /// Device information
  late AndroidDeviceInfo _deviceInfo;
  
  /// Getters
  bool get isGameInstalled => _isGameInstalled;
  bool get isInitialized => _isInitialized;
  String? get gameVersion => _gameVersion;
  
  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Get device information
      final deviceInfoPlugin = DeviceInfoPlugin();
      _deviceInfo = await deviceInfoPlugin.androidInfo;
      
      // Check if game is installed
      await _checkGameInstallation();
      
      _isInitialized = true;
    } catch (e) {
      print('Error initializing GameIntegrationService: $e');
    }
  }
  
  /// Check if 8 Ball Pool is installed
  Future<void> _checkGameInstallation() async {
    try {
      const platform = MethodChannel('com.darkpool.assistant/game_integration');
      final result = await platform.invokeMethod('isAppInstalled', {
        'packageName': _gamePackageName,
      });
      
      _isGameInstalled = result['installed'] ?? false;
      _gameVersion = result['version'];
    } catch (e) {
      print('Error checking game installation: $e');
      _isGameInstalled = false;
    }
  }
  
  /// Launch 8 Ball Pool game
  Future<bool> launchGame() async {
    if (!_isGameInstalled) {
      return false;
    }
    
    try {
      const platform = MethodChannel('com.darkpool.assistant/game_integration');
      final result = await platform.invokeMethod('launchApp', {
        'packageName': _gamePackageName,
      });
      
      return result ?? false;
    } catch (e) {
      print('Error launching game: $e');
      return false;
    }
  }
  
  /// Download and install 8 Ball Pool
  Future<bool> downloadAndInstallGame() async {
    try {
      // Request storage permission
      final storagePermission = await Permission.storage.request();
      if (!storagePermission.isGranted) {
        return false;
      }
      
      // Try to open Play Store first
      final playStoreUri = Uri.parse(_gamePlayStoreUrl);
      if (await canLaunchUrl(playStoreUri)) {
        await launchUrl(playStoreUri, mode: LaunchMode.externalApplication);
        return true;
      }
      
      // Fallback to APK download
      final apkUri = Uri.parse(_gameApkUrl);
      if (await canLaunchUrl(apkUri)) {
        await launchUrl(apkUri, mode: LaunchMode.externalApplication);
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error downloading game: $e');
      return false;
    }
  }
  
  /// Install APK file
  Future<bool> installApk(String apkPath) async {
    try {
      const platform = MethodChannel('com.darkpool.assistant/game_integration');
      final result = await platform.invokeMethod('installApk', {
        'apkPath': apkPath,
      });
      
      return result ?? false;
    } catch (e) {
      print('Error installing APK: $e');
      return false;
    }
  }
  
  /// Get game information
  Future<Map<String, dynamic>?> getGameInfo() async {
    if (!_isGameInstalled) return null;
    
    try {
      const platform = MethodChannel('com.darkpool.assistant/game_integration');
      final result = await platform.invokeMethod('getAppInfo', {
        'packageName': _gamePackageName,
      });
      
      return Map<String, dynamic>.from(result ?? {});
    } catch (e) {
      print('Error getting game info: $e');
      return null;
    }
  }
  
  /// Check for game updates
  Future<bool> checkForUpdates() async {
    try {
      const platform = MethodChannel('com.darkpool.assistant/game_integration');
      final result = await platform.invokeMethod('checkForUpdates', {
        'packageName': _gamePackageName,
      });
      
      return result ?? false;
    } catch (e) {
      print('Error checking for updates: $e');
      return false;
    }
  }
  
  /// Get device compatibility info
  Map<String, dynamic> getDeviceCompatibility() {
    return {
      'isCompatible': _deviceInfo.version.sdkInt >= 21, // Android 5.0+
      'architecture': _deviceInfo.supportedAbis.first,
      'androidVersion': _deviceInfo.version.release,
      'sdkVersion': _deviceInfo.version.sdkInt,
      'manufacturer': _deviceInfo.manufacturer,
      'model': _deviceInfo.model,
      'supportedAbis': _deviceInfo.supportedAbis,
    };
  }
  
  /// Refresh game installation status
  Future<void> refreshGameStatus() async {
    await _checkGameInstallation();
  }
  
  /// Get recommended settings based on device
  Map<String, dynamic> getRecommendedSettings() {
    final compatibility = getDeviceCompatibility();
    final isHighEnd = _deviceInfo.version.sdkInt >= 28; // Android 9+
    
    return {
      'targetFps': isHighEnd ? 60 : 30,
      'maxBounces': isHighEnd ? 5 : 3,
      'enableAdvancedFeatures': isHighEnd,
      'recommendedQuality': isHighEnd ? 'High' : 'Medium',
      'enableAntiAliasing': isHighEnd,
      'enableShadows': isHighEnd,
    };
  }
  
  /// Dispose resources
  void dispose() {
    _isInitialized = false;
  }
}
