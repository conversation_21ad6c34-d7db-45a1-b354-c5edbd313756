-- Project Version: 1.4.1
-- OpenCV Version: 4.11.0+0
-- DartCv Version: ********
-- Android: Targeting API '24' with architecture 'arm64', ABI 'arm64-v8a', and processor 'aarch64'
-- Android: Selected unified Clang toolchain
-- The CXX compiler identification is Clang 12.0.8
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- The C compiler identification is Clang 12.0.8
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Download OpenCV: enabled
-- Detected processor: aarch64
-- os: android, arch: arm64-v8a
-- FETCHCONTENT_BASE_DIR: C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps
-- OpenCV_DIR: C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps/libopencv-src/sdk/native/jni
-- Found OpenCV: C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps/libopencv-src (found version "4.11.0") 
-- FFMPEG_ROOT: C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps/libopencv-src/ffmpeg
-- FFMPEG_ARCH: 
-- Failed to find avdevice version.
-- Failed to find avresample version.
-- Failed to find postproc version.
-- FFMPEG_VERSION: dc07f98-avbuild
-- Found FFMPEG: C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps/libopencv-src/ffmpeg (found version "dc07f98-avbuild") found components: avcodec avformat avutil swscale avfilter swresample missing components: avdevice avresample postproc
-- Added library: dartcv
-- Installing C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps/libopencv-src/ffmpeg/lib/libavcodec.so;C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps/libopencv-src/ffmpeg/lib/libavformat.so;C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps/libopencv-src/ffmpeg/lib/libavutil.so;C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps/libopencv-src/ffmpeg/lib/libswscale.so;C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps/libopencv-src/ffmpeg/lib/libavfilter.so;C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a/_deps/libopencv-src/ffmpeg/lib/libswresample.so to C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/src/main/jniLibs
-- Configuring done
-- Generating done
-- Build files have been written to: C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/arm64-v8a
