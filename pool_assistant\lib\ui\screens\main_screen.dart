import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/game_overlay.dart';
import '../widgets/floating_control_panel.dart';
import '../../services/game_detector.dart';
import '../../services/subscription_service.dart';
import '../../services/activation_service.dart';
import '../../injection/injection_service.dart';
import 'subscription_screen.dart';
import 'activation_screen.dart';
import 'admin_login_screen.dart';

/// Main screen of the application
class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  /// Whether the game is detected
  bool _isGameDetected = false;

  /// Whether the injection is active
  bool _isInjected = false;

  @override
  void initState() {
    super.initState();

    // Initialize services
    _initializeServices();
  }

  /// Initializes the services
  Future<void> _initializeServices() async {
    // Get services
    final gameDetector = Provider.of<GameDetector>(context, listen: false);
    final injectionService = Provider.of<InjectionService>(context, listen: false);

    // Initialize game detector
    await gameDetector.initialize();

    // Initialize injection service
    await injectionService.initialize();

    // Update state
    setState(() {
      _isGameDetected = gameDetector.isGameDetected;
      _isInjected = injectionService.isInjected;
    });

    // Listen for game detection changes
    gameDetector.gameStateStream.listen((_) {
      setState(() {
        _isGameDetected = gameDetector.isGameDetected;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.blue.shade900,
                  Colors.black,
                ],
              ),
            ),
          ),

          // Content
          SafeArea(
            child: _isGameDetected
                ? _buildGameDetectedContent()
                : _buildGameNotDetectedContent(),
          ),

          // Floating control panel (always visible)
          const FloatingControlPanel(),
        ],
      ),
    );
  }

  /// Builds the content when the game is detected
  Widget _buildGameDetectedContent() {
    return Stack(
      children: [
        // Game overlay
        const GameOverlay(),

        // Status indicator
        Positioned(
          top: 16,
          right: 16,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green.withAlpha(204), // 0.8 * 255 = 204
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 16,
                ),
                SizedBox(width: 8),
                Text(
                  'Game Detected',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the content when the game is not detected
  Widget _buildGameNotDetectedContent() {
    return Consumer<SubscriptionService>(
      builder: (context, subscriptionService, child) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.sports_hockey,
                color: Colors.white,
                size: 80,
              ),
              const SizedBox(height: 24),
              const Text(
                '8 Ball Pool Assistant',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Consumer<InjectionService>(
                builder: (context, injectionService, child) {
                  return Column(
                    children: [
                      if (injectionService.isNoRootMode) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.blue.withAlpha(100),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: Colors.blue, width: 1),
                          ),
                          child: const Text(
                            'No-Root Mode: Overlay Features Active',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                      ] else if (injectionService.hasRootAccess) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.green.withAlpha(100),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: Colors.green, width: 1),
                          ),
                          child: const Text(
                            'Root Mode: Full Features Available',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                      ],
                      if (subscriptionService.hasActiveSubscription) ...[
                        Text(
                          'Active Plan: ${subscriptionService.currentPlanName}',
                          style: const TextStyle(
                            color: Colors.green,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Days Remaining: ${subscriptionService.daysRemaining}',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ] else ...[
                        const Text(
                          'No active subscription',
                          style: TextStyle(
                            color: Colors.orange,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
              const SizedBox(height: 16),
              const Text(
                'Waiting for 8 Ball Pool game...',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 32),
              Wrap(
                alignment: WrapAlignment.center,
                spacing: 16,
                runSpacing: 16,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      // Launch the 8 Ball Pool game
                      _launchGame();
                    },
                    icon: const Icon(Icons.sports_esports),
                    label: const Text('Launch 8 Ball Pool'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      backgroundColor: Colors.blue,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Navigate to subscription screen
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SubscriptionScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.card_membership),
                    label: const Text('Subscription Plans'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      backgroundColor: Colors.green,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Navigate to activation screen
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ActivationScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.vpn_key),
                    label: const Text('Activate Code'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      backgroundColor: Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextButton.icon(
                onPressed: () {
                  // Navigate to admin login screen
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AdminLoginScreen(),
                    ),
                  );
                },
                icon: const Icon(Icons.admin_panel_settings, size: 16),
                label: const Text('Admin Panel'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.white.withAlpha(150),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Make sure you have the game installed\nand have granted necessary permissions.',
                style: TextStyle(
                  color: Colors.white54,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  /// Launches the 8 Ball Pool game
  Future<void> _launchGame() async {
    final injectionService = Provider.of<InjectionService>(context, listen: false);

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Launching 8 Ball Pool...'),
        duration: Duration(seconds: 3),
      ),
    );

    try {
      // Try to launch the game
      final success = await injectionService.launchGame();

      if (success && mounted) {
        // Start injection/overlay after a short delay
        await Future.delayed(const Duration(seconds: 2));
        final injectionSuccess = await injectionService.startInjection();

        setState(() {
          _isInjected = injectionSuccess;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                injectionService.isNoRootMode
                  ? '8 Ball Pool launched! Running in no-root mode with overlay features.'
                  : '8 Ball Pool launched! Full features enabled.',
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to launch 8 Ball Pool. Please install the game from Play Store.'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error launching game: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }
}
