import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/game_overlay.dart';
import '../widgets/floating_control_panel.dart';
import '../widgets/fire_button.dart';
import '../widgets/animated_background.dart';
import '../../services/game_detector.dart';
import '../../services/subscription_service.dart';
import '../../services/activation_service.dart';
import '../../services/game_integration_service.dart';
import '../../injection/injection_service.dart';
import 'subscription_screen.dart';
import 'activation_screen.dart';
import 'admin_login_screen.dart';
import 'developer_info_screen.dart';
import 'advanced_training_screen.dart';

/// Main screen of the application
class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  /// Whether the game is detected
  bool _isGameDetected = false;

  /// Whether the injection is active
  bool _isInjected = false;

  @override
  void initState() {
    super.initState();

    // Initialize services
    _initializeServices();
  }

  /// Initializes the services
  Future<void> _initializeServices() async {
    // Get services
    final gameDetector = Provider.of<GameDetector>(context, listen: false);
    final injectionService = Provider.of<InjectionService>(context, listen: false);

    // Initialize game detector
    await gameDetector.initialize();

    // Initialize injection service
    await injectionService.initialize();

    // Update state
    setState(() {
      _isGameDetected = gameDetector.isGameDetected;
      _isInjected = injectionService.isInjected;
    });

    // Listen for game detection changes
    gameDetector.gameStateStream.listen((_) {
      setState(() {
        _isGameDetected = gameDetector.isGameDetected;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Animated Background
          const PoolAnimatedBackground(),

          // Content
          SafeArea(
            child: _isGameDetected
                ? _buildGameDetectedContent()
                : _buildGameNotDetectedContent(),
          ),

          // Floating control panel (always visible)
          const FloatingControlPanel(),
        ],
      ),
    );
  }

  /// Builds the content when the game is detected
  Widget _buildGameDetectedContent() {
    return Stack(
      children: [
        // Game overlay
        const GameOverlay(),

        // Status indicator
        Positioned(
          top: 16,
          right: 16,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green.withAlpha(204), // 0.8 * 255 = 204
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 16,
                ),
                SizedBox(width: 8),
                Text(
                  'Game Detected',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the content when the game is not detected
  Widget _buildGameNotDetectedContent() {
    return Consumer<SubscriptionService>(
      builder: (context, subscriptionService, child) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo with glow effect
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: const LinearGradient(
                    colors: [Color(0xFF1B263B), Color(0xFF415A77)],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF4CAF50).withOpacity(0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.sports_esports,
                  size: 50,
                  color: Color(0xFF4CAF50),
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'DARK POOL',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 2,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Advanced 8 Ball Pool Assistant',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 5),
              Text(
                'by Hazem Farouk',
                style: TextStyle(
                  color: const Color(0xFF4CAF50).withOpacity(0.8),
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                ),
              ),
              const SizedBox(height: 16),
              Consumer<InjectionService>(
                builder: (context, injectionService, child) {
                  return Column(
                    children: [
                      if (injectionService.isNoRootMode) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.blue.withAlpha(100),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: Colors.blue, width: 1),
                          ),
                          child: const Text(
                            'No-Root Mode: Overlay Features Active',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                      ] else if (injectionService.hasRootAccess) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.green.withAlpha(100),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: Colors.green, width: 1),
                          ),
                          child: const Text(
                            'Root Mode: Full Features Available',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                      ],
                      if (subscriptionService.hasActiveSubscription) ...[
                        Text(
                          'Active Plan: ${subscriptionService.currentPlanName}',
                          style: const TextStyle(
                            color: Colors.green,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Days Remaining: ${subscriptionService.daysRemaining}',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ] else ...[
                        const Text(
                          'No active subscription',
                          style: TextStyle(
                            color: Colors.orange,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
              const SizedBox(height: 16),
              const Text(
                'Waiting for 8 Ball Pool game...',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 32),
              Column(
                children: [
                  // Launch Game Button
                  PrimaryFireButton(
                    text: 'Launch 8 Ball Pool',
                    icon: Icons.sports_esports,
                    onPressed: _launchGame,
                  ),

                  const SizedBox(height: 16),

                  // Advanced Training Button
                  FireButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AdvancedTrainingScreen(),
                        ),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
                        ),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF2196F3).withValues(alpha: 0.3),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.psychology,
                            color: Colors.white,
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Advanced Training Mode',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Action Buttons Row
                  Row(
                    children: [
                      Expanded(
                        child: FireButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SubscriptionScreen(),
                              ),
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Column(
                              children: [
                                Icon(Icons.card_membership, color: Colors.white, size: 20),
                                SizedBox(height: 4),
                                Text(
                                  'Subscription',
                                  style: TextStyle(color: Colors.white, fontSize: 12),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(width: 12),

                      Expanded(
                        child: FireButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const ActivationScreen(),
                              ),
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFFF9800), Color(0xFFE65100)],
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Column(
                              children: [
                                Icon(Icons.vpn_key, color: Colors.white, size: 20),
                                SizedBox(height: 4),
                                Text(
                                  'Activate Code',
                                  style: TextStyle(color: Colors.white, fontSize: 12),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(width: 12),

                      Expanded(
                        child: FireButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const DeveloperInfoScreen(),
                              ),
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFF9C27B0), Color(0xFF6A1B9A)],
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Column(
                              children: [
                                Icon(Icons.person, color: Colors.white, size: 20),
                                SizedBox(height: 4),
                                Text(
                                  'Developer',
                                  style: TextStyle(color: Colors.white, fontSize: 12),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              FireButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AdminLoginScreen(),
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.white.withOpacity(0.3)),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.admin_panel_settings,
                        color: Colors.white.withOpacity(0.8),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Admin Panel',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Make sure you have the game installed\nand have granted necessary permissions.',
                style: TextStyle(
                  color: Colors.white54,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  /// Launches the 8 Ball Pool game
  Future<void> _launchGame() async {
    final injectionService = Provider.of<InjectionService>(context, listen: false);
    final gameIntegrationService = Provider.of<GameIntegrationService>(context, listen: false);

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Checking 8 Ball Pool installation...'),
        duration: Duration(seconds: 3),
      ),
    );

    try {
      // Refresh game status
      await gameIntegrationService.refreshGameStatus();

      if (!gameIntegrationService.isGameInstalled) {
        // Game not installed, show install dialog
        _showGameInstallDialog();
        return;
      }

      // Game is installed, try to launch it
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Launching 8 Ball Pool...'),
            duration: Duration(seconds: 2),
          ),
        );
      }

      final success = await gameIntegrationService.launchGame();

      if (success && mounted) {
        // Start injection/overlay after a short delay
        await Future.delayed(const Duration(seconds: 2));
        final injectionSuccess = await injectionService.startInjection();

        setState(() {
          _isInjected = injectionSuccess;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                injectionService.isNoRootMode
                  ? '8 Ball Pool launched! Running in no-root mode with overlay features.'
                  : '8 Ball Pool launched! Full features enabled.',
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to launch 8 Ball Pool. Please try again.'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  /// Shows game installation dialog
  void _showGameInstallDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1B263B),
        title: const Text(
          '8 Ball Pool Not Found',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'The 8 Ball Pool game is not installed on your device. Would you like to install it?',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _installGame();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
            ),
            child: const Text('Install Game'),
          ),
        ],
      ),
    );
  }

  /// Installs the 8 Ball Pool game
  Future<void> _installGame() async {
    final gameIntegrationService = Provider.of<GameIntegrationService>(context, listen: false);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening Play Store...'),
        duration: Duration(seconds: 2),
      ),
    );

    try {
      final success = await gameIntegrationService.downloadAndInstallGame();

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Redirected to Play Store. Please install 8 Ball Pool and return to this app.'),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 5),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to open Play Store. Please install 8 Ball Pool manually.'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }
}
