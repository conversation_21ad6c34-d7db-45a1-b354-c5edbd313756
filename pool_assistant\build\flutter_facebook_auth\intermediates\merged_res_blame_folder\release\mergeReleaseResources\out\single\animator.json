[{"merged": "app.meedu.flutter_facebook_auth-release-33:/animator/fragment_open_exit.xml", "source": "app.meedu.flutter_facebook_auth-fragment-1.7.1-9:/animator/fragment_open_exit.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/animator/fragment_open_enter.xml", "source": "app.meedu.flutter_facebook_auth-fragment-1.7.1-9:/animator/fragment_open_enter.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/animator/fragment_fade_exit.xml", "source": "app.meedu.flutter_facebook_auth-fragment-1.7.1-9:/animator/fragment_fade_exit.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/animator/fragment_close_enter.xml", "source": "app.meedu.flutter_facebook_auth-fragment-1.7.1-9:/animator/fragment_close_enter.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/animator/fragment_close_exit.xml", "source": "app.meedu.flutter_facebook_auth-fragment-1.7.1-9:/animator/fragment_close_exit.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/animator/fragment_fade_enter.xml", "source": "app.meedu.flutter_facebook_auth-fragment-1.7.1-9:/animator/fragment_fade_enter.xml"}]