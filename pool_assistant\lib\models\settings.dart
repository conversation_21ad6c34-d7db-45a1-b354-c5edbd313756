import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';

/// Stores user settings for the application
class AppSettings {
  /// Whether to show trajectory lines
  bool showTrajectoryLines;

  /// Whether to show pocket highlights
  bool showPocketHighlights;

  /// Whether to enable auto-aim feature
  bool enableAutoAim;

  /// Whether to enable fast mode (simplified physics)
  bool enableFastMode;

  /// Whether to enable streaming mode (hide overlays)
  bool enableStreamingMode;

  /// Maximum number of bounces to predict
  int maxBounces;

  /// Target frames per second
  int targetFps;

  /// Line thickness for trajectory lines
  double lineThickness;

  /// Color of the cue ball trajectory line
  Color cueBallLineColor;

  /// Color of the object ball trajectory lines
  Color objectBallLineColor;

  /// Color of the pocket highlight
  Color pocketHighlightColor;

  /// X-axis calibration offset
  double calibrationOffsetX;

  /// Y-axis calibration offset
  double calibrationOffsetY;

  /// Scale factor for display calibration
  double calibrationScale;

  /// Whether to enable glow effects
  bool enableGlowEffects;

  /// Aim line color
  Color aimLineColor;

  /// Collision marker color
  Color collisionMarkerColor;

  AppSettings({
    this.showTrajectoryLines = true,
    this.showPocketHighlights = true,
    this.enableAutoAim = false,
    this.enableFastMode = false,
    this.enableStreamingMode = false,
    this.maxBounces = 3,
    this.targetFps = 60,
    this.lineThickness = 2.0,
    this.cueBallLineColor = const Color(0xFFFFFFFF),
    this.objectBallLineColor = const Color(0xFFFFFF00),
    this.pocketHighlightColor = const Color(0xFF00FF00),
    this.calibrationOffsetX = 0.0,
    this.calibrationOffsetY = 0.0,
    this.calibrationScale = 1.0,
    this.enableGlowEffects = true,
    this.aimLineColor = const Color(0xFF00FFFF),
    this.collisionMarkerColor = const Color(0xFFFF0000),
  });

  /// Loads settings from shared preferences
  static Future<AppSettings> load() async {
    final prefs = await SharedPreferences.getInstance();

    return AppSettings(
      showTrajectoryLines: prefs.getBool('showTrajectoryLines') ?? true,
      showPocketHighlights: prefs.getBool('showPocketHighlights') ?? true,
      enableAutoAim: prefs.getBool('enableAutoAim') ?? false,
      enableFastMode: prefs.getBool('enableFastMode') ?? false,
      enableStreamingMode: prefs.getBool('enableStreamingMode') ?? false,
      maxBounces: prefs.getInt('maxBounces') ?? 3,
      targetFps: prefs.getInt('targetFps') ?? 60,
      lineThickness: prefs.getDouble('lineThickness') ?? 2.0,
      cueBallLineColor: Color(prefs.getInt('cueBallLineColor') ?? 0xFFFFFFFF),
      objectBallLineColor: Color(prefs.getInt('objectBallLineColor') ?? 0xFFFFFF00),
      pocketHighlightColor: Color(prefs.getInt('pocketHighlightColor') ?? 0xFF00FF00),
      calibrationOffsetX: prefs.getDouble('calibrationOffsetX') ?? 0.0,
      calibrationOffsetY: prefs.getDouble('calibrationOffsetY') ?? 0.0,
      calibrationScale: prefs.getDouble('calibrationScale') ?? 1.0,
      enableGlowEffects: prefs.getBool('enableGlowEffects') ?? true,
      aimLineColor: Color(prefs.getInt('aimLineColor') ?? 0xFF00FFFF),
      collisionMarkerColor: Color(prefs.getInt('collisionMarkerColor') ?? 0xFFFF0000),
    );
  }

  /// Saves settings to shared preferences
  Future<void> save() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setBool('showTrajectoryLines', showTrajectoryLines);
    await prefs.setBool('showPocketHighlights', showPocketHighlights);
    await prefs.setBool('enableAutoAim', enableAutoAim);
    await prefs.setBool('enableFastMode', enableFastMode);
    await prefs.setBool('enableStreamingMode', enableStreamingMode);
    await prefs.setInt('maxBounces', maxBounces);
    await prefs.setInt('targetFps', targetFps);
    await prefs.setDouble('lineThickness', lineThickness);
    await prefs.setInt('cueBallLineColor', cueBallLineColor.value);
    await prefs.setInt('objectBallLineColor', objectBallLineColor.value);
    await prefs.setInt('pocketHighlightColor', pocketHighlightColor.value);
    await prefs.setDouble('calibrationOffsetX', calibrationOffsetX);
    await prefs.setDouble('calibrationOffsetY', calibrationOffsetY);
    await prefs.setDouble('calibrationScale', calibrationScale);
    await prefs.setBool('enableGlowEffects', enableGlowEffects);
    await prefs.setInt('aimLineColor', aimLineColor.value);
    await prefs.setInt('collisionMarkerColor', collisionMarkerColor.value);
  }

  /// Creates a copy of this settings object with the given fields replaced
  AppSettings copyWith({
    bool? showTrajectoryLines,
    bool? showPocketHighlights,
    bool? enableAutoAim,
    bool? enableFastMode,
    bool? enableStreamingMode,
    int? maxBounces,
    int? targetFps,
    double? lineThickness,
    Color? cueBallLineColor,
    Color? objectBallLineColor,
    Color? pocketHighlightColor,
    double? calibrationOffsetX,
    double? calibrationOffsetY,
    double? calibrationScale,
    bool? enableGlowEffects,
    Color? aimLineColor,
    Color? collisionMarkerColor,
  }) {
    return AppSettings(
      showTrajectoryLines: showTrajectoryLines ?? this.showTrajectoryLines,
      showPocketHighlights: showPocketHighlights ?? this.showPocketHighlights,
      enableAutoAim: enableAutoAim ?? this.enableAutoAim,
      enableFastMode: enableFastMode ?? this.enableFastMode,
      enableStreamingMode: enableStreamingMode ?? this.enableStreamingMode,
      maxBounces: maxBounces ?? this.maxBounces,
      targetFps: targetFps ?? this.targetFps,
      lineThickness: lineThickness ?? this.lineThickness,
      cueBallLineColor: cueBallLineColor ?? this.cueBallLineColor,
      objectBallLineColor: objectBallLineColor ?? this.objectBallLineColor,
      pocketHighlightColor: pocketHighlightColor ?? this.pocketHighlightColor,
      calibrationOffsetX: calibrationOffsetX ?? this.calibrationOffsetX,
      calibrationOffsetY: calibrationOffsetY ?? this.calibrationOffsetY,
      calibrationScale: calibrationScale ?? this.calibrationScale,
      enableGlowEffects: enableGlowEffects ?? this.enableGlowEffects,
      aimLineColor: aimLineColor ?? this.aimLineColor,
      collisionMarkerColor: collisionMarkerColor ?? this.collisionMarkerColor,
    );
  }
}
