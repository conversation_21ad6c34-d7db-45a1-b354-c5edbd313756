<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="Base.DialogWindowTitle.AppCompat">
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="android:Widget">
        <item name="android:layout_gravity">center_vertical|start</item>
        <item name="android:paddingEnd">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="android:Widget">
        <item name="android:layout_marginEnd">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="android:Widget">
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="android:Widget">
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="android:Widget">
        <item name="android:layout_marginStart">16dp</item>
        <item name="android:textAlignment">viewEnd</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="android:Widget">
        <item name="android:layout_marginStart">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="android:Widget">
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="android:Widget">
        <item name="android:layout_marginStart">16dp</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="android:Widget">
        <item name="android:paddingStart">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingEnd">4dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="android:Widget">
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="android:Widget">
        <item name="android:layout_toStartOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="android:Widget">
        <item name="android:layout_alignParentEnd">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toStartOf">@android:id/icon2</item>
        <item name="android:layout_toEndOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="android:Widget">
        <item name="android:layout_marginStart">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="android:Widget">
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingStart">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
</resources>