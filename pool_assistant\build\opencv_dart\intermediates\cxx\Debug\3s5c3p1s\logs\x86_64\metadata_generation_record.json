[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx\\Debug\\3s5c3p1s\\x86_64\\android_gradle_build.json due to:", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx\\Debug\\3s5c3p1s\\x86_64'", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx\\Debug\\3s5c3p1s\\x86_64'", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\opencv_dart-1.4.1\\\\src\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\23.1.7779620\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\23.1.7779620\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\23.1.7779620\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\pool\\\\pool_assistant\\\\build\\\\opencv_dart\\\\intermediates\\\\cxx\\\\Debug\\\\3s5c3p1s\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\pool\\\\pool_assistant\\\\build\\\\opencv_dart\\\\intermediates\\\\cxx\\\\Debug\\\\3s5c3p1s\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\opencv_dart-1.4.1\\\\android\\\\.cxx\\\\Debug\\\\3s5c3p1s\\\\x86_64\" ^\n  -GNinja ^\n  \"-DANDROID_ARM_NEON=TRUE\" ^\n  \"-DANDROID_STL=c++_static\" ^\n  \"-DCMAKE_INSTALL_PREFIX=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\opencv_dart-1.4.1\\\\android/src/main/jniLibs\"\n", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\opencv_dart-1.4.1\\\\src\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\23.1.7779620\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\23.1.7779620\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\ndk\\\\23.1.7779620\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\pool\\\\pool_assistant\\\\build\\\\opencv_dart\\\\intermediates\\\\cxx\\\\Debug\\\\3s5c3p1s\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\pool\\\\pool_assistant\\\\build\\\\opencv_dart\\\\intermediates\\\\cxx\\\\Debug\\\\3s5c3p1s\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\opencv_dart-1.4.1\\\\android\\\\.cxx\\\\Debug\\\\3s5c3p1s\\\\x86_64\" ^\n  -GNinja ^\n  \"-DANDROID_ARM_NEON=TRUE\" ^\n  \"-DANDROID_STL=c++_static\" ^\n  \"-DCMAKE_INSTALL_PREFIX=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\opencv_dart-1.4.1\\\\android/src/main/jniLibs\"\n", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx\\Debug\\3s5c3p1s\\x86_64\\compile_commands.json.bin normally", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx\\Debug\\3s5c3p1s\\x86_64\\compile_commands.json to C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\android\\.cxx\\tools\\debug\\x86_64\\compile_commands.json", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\opencv_dart-1.4.1\\src\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]