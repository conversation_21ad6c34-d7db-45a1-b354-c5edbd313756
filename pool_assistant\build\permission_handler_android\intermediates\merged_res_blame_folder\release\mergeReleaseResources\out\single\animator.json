[{"merged": "com.baseflow.permissionhandler.permission_handler_android-release-25:/animator/fragment_open_enter.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-fragment-1.7.1-5:/animator/fragment_open_enter.xml"}, {"merged": "com.baseflow.permissionhandler.permission_handler_android-release-25:/animator/fragment_fade_exit.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-fragment-1.7.1-5:/animator/fragment_fade_exit.xml"}, {"merged": "com.baseflow.permissionhandler.permission_handler_android-release-25:/animator/fragment_close_enter.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-fragment-1.7.1-5:/animator/fragment_close_enter.xml"}, {"merged": "com.baseflow.permissionhandler.permission_handler_android-release-25:/animator/fragment_open_exit.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-fragment-1.7.1-5:/animator/fragment_open_exit.xml"}, {"merged": "com.baseflow.permissionhandler.permission_handler_android-release-25:/animator/fragment_fade_enter.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-fragment-1.7.1-5:/animator/fragment_fade_enter.xml"}, {"merged": "com.baseflow.permissionhandler.permission_handler_android-release-25:/animator/fragment_close_exit.xml", "source": "com.baseflow.permissionhandler.permission_handler_android-fragment-1.7.1-5:/animator/fragment_close_exit.xml"}]