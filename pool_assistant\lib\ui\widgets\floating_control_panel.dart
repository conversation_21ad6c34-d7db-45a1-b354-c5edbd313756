import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../../services/settings_service.dart';
import '../../injection/injection_service.dart';

/// Widget that displays a floating control panel
class FloatingControlPanel extends StatefulWidget {
  const FloatingControlPanel({super.key});

  @override
  State<FloatingControlPanel> createState() => _FloatingControlPanelState();
}

class _FloatingControlPanelState extends State<FloatingControlPanel> {
  /// Whether the panel is expanded
  bool _isExpanded = false;

  /// Whether the panel is being dragged
  bool _isDragging = false;

  /// Position of the panel
  Offset _position = const Offset(20, 100);

  /// Current tab index
  int _currentTabIndex = 0;

  /// Whether the panel is minimized
  bool _isMinimized = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// Toggles the expanded state of the panel
  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      _isMinimized = false;
    });
  }

  /// Toggles the minimized state of the panel
  void _toggleMinimized() {
    setState(() {
      _isMinimized = !_isMinimized;
      if (_isMinimized) {
        _isExpanded = false;
      }
    });
  }

  /// Changes the current tab
  void _changeTab(int index) {
    setState(() {
      _currentTabIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: GestureDetector(
        onPanStart: (details) {
          setState(() {
            _isDragging = true;
          });
        },
        onPanUpdate: (details) {
          setState(() {
            _position = Offset(
              _position.dx + details.delta.dx,
              _position.dy + details.delta.dy,
            );
          });
        },
        onPanEnd: (details) {
          setState(() {
            _isDragging = false;
          });
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: _isMinimized ? 70 : (_isExpanded ? 320 : 220),
          height: _isMinimized ? 70 : (_isExpanded ? 450 : 70),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF0D1B2A).withOpacity(0.95),
                const Color(0xFF1B263B).withOpacity(0.95),
                const Color(0xFF415A77).withOpacity(0.95),
              ],
            ),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: const Color(0xFF4CAF50).withOpacity(0.6),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF4CAF50).withOpacity(0.3),
                blurRadius: 20,
                spreadRadius: 3,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.5),
                blurRadius: 15,
                spreadRadius: 1,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: _isMinimized
              ? _buildMinimizedPanel()
              : Column(
                  children: [
                    _buildPanelHeader(),
                    if (_isExpanded) ...[
                      _buildTabBar(),
                      Expanded(
                        child: _buildTabContent(),
                      ),
                    ],
                  ],
                ),
        ),
      ),
    );
  }

  /// Builds the minimized panel
  Widget _buildMinimizedPanel() {
    return InkWell(
      onTap: _toggleMinimized,
      borderRadius: BorderRadius.circular(35),
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF0D1B2A),
              Color(0xFF1B263B),
              Color(0xFF415A77),
            ],
          ),
          border: Border.all(
            color: const Color(0xFF4CAF50),
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF4CAF50).withOpacity(0.5),
              blurRadius: 15,
              spreadRadius: 3,
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 10,
              spreadRadius: 1,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: const Center(
          child: Icon(
            Icons.sports_esports,
            color: Color(0xFF4CAF50),
            size: 35,
          ),
        ),
      ),
    );
  }

  /// Builds the panel header
  Widget _buildPanelHeader() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF4CAF50).withValues(alpha: 0.2),
            const Color(0xFF2E7D32).withValues(alpha: 0.1),
          ],
        ),
        border: Border(
          bottom: _isExpanded
              ? BorderSide(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
                  width: 2,
                )
              : BorderSide.none,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(15),
          topRight: Radius.circular(15),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.sports_esports,
                    color: Color(0xFF4CAF50),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Flexible(
                  child: Text(
                    'Dark Pool',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
                child: IconButton(
                  icon: Icon(
                    _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: const Color(0xFF4CAF50),
                  ),
                  onPressed: _toggleExpanded,
                  padding: const EdgeInsets.all(6),
                  constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
                  iconSize: 20,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFFF9800).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xFFFF9800).withValues(alpha: 0.5),
                    width: 1,
                  ),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.remove,
                    color: Color(0xFFFF9800),
                  ),
                  onPressed: _toggleMinimized,
                  padding: const EdgeInsets.all(6),
                  constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
                  iconSize: 20,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds the tab bar
  Widget _buildTabBar() {
    return Container(
      height: 55,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFF0D1B2A).withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(child: _buildTabButton(0, 'Basic', Icons.tune)),
          Expanded(child: _buildTabButton(1, 'Advanced', Icons.settings)),
          Expanded(child: _buildTabButton(2, 'Calibration', Icons.straighten)),
        ],
      ),
    );
  }

  /// Builds a tab button
  Widget _buildTabButton(int index, String label, IconData icon) {
    final isSelected = _currentTabIndex == index;

    return InkWell(
      onTap: () => _changeTab(index),
      borderRadius: BorderRadius.circular(10),
      child: Container(
        margin: const EdgeInsets.all(4),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF4CAF50).withValues(alpha: 0.8),
                    const Color(0xFF2E7D32).withValues(alpha: 0.6),
                  ],
                )
              : null,
          borderRadius: BorderRadius.circular(10),
          border: isSelected
              ? Border.all(
                  color: const Color(0xFF4CAF50),
                  width: 1,
                )
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.7),
              size: 16,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.7),
                fontSize: 11,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the tab content
  Widget _buildTabContent() {
    switch (_currentTabIndex) {
      case 0:
        return _buildBasicTab();
      case 1:
        return _buildAdvancedTab();
      case 2:
        return _buildCalibrationTab();
      default:
        return const SizedBox.shrink();
    }
  }

  /// Builds the basic tab
  Widget _buildBasicTab() {
    return Consumer<SettingsService>(
      builder: (context, settingsService, child) {
        final settings = settingsService.settings;

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSwitchSetting(
                'Show Trajectory Lines',
                settings.showTrajectoryLines,
                (value) => settingsService.updateSetting(
                  name: 'showTrajectoryLines',
                  value: value,
                ),
              ),
              _buildSwitchSetting(
                'Show Pocket Highlights',
                settings.showPocketHighlights,
                (value) => settingsService.updateSetting(
                  name: 'showPocketHighlights',
                  value: value,
                ),
              ),
              _buildSwitchSetting(
                'Streaming Mode',
                settings.enableStreamingMode,
                (value) => settingsService.updateSetting(
                  name: 'enableStreamingMode',
                  value: value,
                ),
              ),
              const SizedBox(height: 16),
              _buildInjectionControls(),
            ],
          ),
        );
      },
    );
  }

  /// Builds the advanced tab
  Widget _buildAdvancedTab() {
    return Consumer<SettingsService>(
      builder: (context, settingsService, child) {
        final settings = settingsService.settings;

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSwitchSetting(
                'Auto Aim',
                settings.enableAutoAim,
                (value) => settingsService.updateSetting(
                  name: 'enableAutoAim',
                  value: value,
                ),
              ),
              _buildSwitchSetting(
                'Fast Mode',
                settings.enableFastMode,
                (value) => settingsService.updateSetting(
                  name: 'enableFastMode',
                  value: value,
                ),
              ),
              const SizedBox(height: 16),
              _buildSliderSetting(
                'Max Bounces',
                settings.maxBounces.toDouble(),
                1,
                5,
                (value) => settingsService.updateSetting(
                  name: 'maxBounces',
                  value: value.round(),
                ),
              ),
              _buildSliderSetting(
                'Target FPS',
                settings.targetFps.toDouble(),
                30,
                60,
                (value) => settingsService.updateSetting(
                  name: 'targetFps',
                  value: value.round(),
                ),
                divisions: 1,
              ),
              _buildSliderSetting(
                'Line Thickness',
                settings.lineThickness,
                1,
                5,
                (value) => settingsService.updateSetting(
                  name: 'lineThickness',
                  value: value,
                ),
              ),
              const SizedBox(height: 16),
              _buildColorSetting(
                'Cue Ball Line Color',
                settings.cueBallLineColor,
                (value) => settingsService.updateSetting(
                  name: 'cueBallLineColor',
                  value: value,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Builds the calibration tab
  Widget _buildCalibrationTab() {
    return Consumer<SettingsService>(
      builder: (context, settingsService, child) {
        final settings = settingsService.settings;

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSliderSetting(
                'X Offset',
                settings.calibrationOffsetX,
                -100,
                100,
                (value) => settingsService.updateSetting(
                  name: 'calibrationOffsetX',
                  value: value,
                ),
              ),
              _buildSliderSetting(
                'Y Offset',
                settings.calibrationOffsetY,
                -100,
                100,
                (value) => settingsService.updateSetting(
                  name: 'calibrationOffsetY',
                  value: value,
                ),
              ),
              _buildSliderSetting(
                'Scale',
                settings.calibrationScale,
                0.5,
                1.5,
                (value) => settingsService.updateSetting(
                  name: 'calibrationScale',
                  value: value,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => settingsService.updateSettings(
                  settings.copyWith(
                    calibrationOffsetX: 0,
                    calibrationOffsetY: 0,
                    calibrationScale: 1.0,
                  ),
                ),
                child: const Text('Reset Calibration'),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Builds a switch setting
  Widget _buildSwitchSetting(
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.green,
          ),
        ],
      ),
    );
  }

  /// Builds a slider setting
  Widget _buildSliderSetting(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged, {
    int? divisions,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              Text(
                value.toStringAsFixed(1),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
            activeColor: Colors.green,
          ),
        ],
      ),
    );
  }

  /// Builds a color setting
  Widget _buildColorSetting(
    String label,
    Color value,
    Function(Color) onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
          InkWell(
            onTap: () => _showColorPicker(value, onChanged),
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: value,
                border: Border.all(
                  color: Colors.white,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Shows a color picker dialog
  void _showColorPicker(Color initialColor, Function(Color) onColorChanged) {
    showDialog(
      context: context,
      builder: (context) {
        Color selectedColor = initialColor;

        return AlertDialog(
          title: const Text('Pick a color'),
          content: SingleChildScrollView(
            child: ColorPicker(
              pickerColor: initialColor,
              onColorChanged: (color) {
                selectedColor = color;
              },
              pickerAreaHeightPercent: 0.8,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                onColorChanged(selectedColor);
                Navigator.of(context).pop();
              },
              child: const Text('Select'),
            ),
          ],
        );
      },
    );
  }

  /// Builds the injection controls
  Widget _buildInjectionControls() {
    return Consumer<InjectionService>(
      builder: (context, injectionService, child) {
        final isInjected = injectionService.isInjected;
        final hasRootAccess = injectionService.hasRootAccess;
        final isNoRootMode = injectionService.isNoRootMode;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status indicator
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: isInjected
                    ? (isNoRootMode ? Colors.blue : Colors.green)
                    : Colors.grey,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isInjected ? Icons.check_circle : Icons.circle_outlined,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      isInjected
                          ? (isNoRootMode ? 'Overlay Active' : 'Injection Active')
                          : 'Features Inactive',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 8),

            // Control buttons
            if (!hasRootAccess && !isNoRootMode) ...[
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withOpacity(0.5)),
                ),
                child: const Text(
                  'Root access required for full features',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 11,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ] else ...[
              // Main toggle button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: isInjected
                      ? injectionService.stopInjection
                      : injectionService.startInjection,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: isInjected ? Colors.red : Colors.green,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  icon: Icon(
                    isInjected ? Icons.stop : Icons.play_arrow,
                    size: 16,
                  ),
                  label: Text(
                    isInjected ? 'Stop' : 'Start',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ),

              if (isInjected) ...[
                const SizedBox(height: 6),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: injectionService.fixLoginIssues,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      padding: const EdgeInsets.symmetric(vertical: 6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    icon: const Icon(Icons.build, size: 14),
                    label: const Text(
                      'Fix Issues',
                      style: TextStyle(fontSize: 11),
                    ),
                  ),
                ),
              ],
            ],
          ],
        );
      },
    );
  }
}
