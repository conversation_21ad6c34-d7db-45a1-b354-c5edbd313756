  
FlutterEngine android.app.Activity  GameDetectorService android.app.Activity  InjectionService android.app.Activity  
MethodChannel android.app.Activity  configureFlutterEngine android.app.Activity  invoke android.app.Activity  Context android.content  
FlutterEngine android.content.Context  GameDetectorService android.content.Context  InjectionService android.content.Context  
MethodChannel android.content.Context  configureFlutterEngine android.content.Context  getPACKAGEManager android.content.Context  getPackageManager android.content.Context  invoke android.content.Context  packageManager android.content.Context  setPackageManager android.content.Context  
FlutterEngine android.content.ContextWrapper  GameDetectorService android.content.ContextWrapper  InjectionService android.content.ContextWrapper  
MethodChannel android.content.ContextWrapper  configureFlutterEngine android.content.ContextWrapper  invoke android.content.ContextWrapper  PackageInfo android.content.pm  PackageManager android.content.pm  getPackageInfo !android.content.pm.PackageManager  Log android.util  e android.util.Log  
FlutterEngine  android.view.ContextThemeWrapper  GameDetectorService  android.view.ContextThemeWrapper  InjectionService  android.view.ContextThemeWrapper  
MethodChannel  android.view.ContextThemeWrapper  configureFlutterEngine  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  Boolean  com.poolassistant.pool_assistant  Double  com.poolassistant.pool_assistant  	Exception  com.poolassistant.pool_assistant  File  com.poolassistant.pool_assistant  GAME_PACKAGE_NAME  com.poolassistant.pool_assistant  GameDetectorService  com.poolassistant.pool_assistant  InjectionService  com.poolassistant.pool_assistant  Int  com.poolassistant.pool_assistant  	JSONArray  com.poolassistant.pool_assistant  
JSONObject  com.poolassistant.pool_assistant  Log  com.poolassistant.pool_assistant  MainActivity  com.poolassistant.pool_assistant  Map  com.poolassistant.pool_assistant  
MethodChannel  com.poolassistant.pool_assistant  Runtime  com.poolassistant.pool_assistant  String  com.poolassistant.pool_assistant  TAG  com.poolassistant.pool_assistant  Timer  com.poolassistant.pool_assistant  arrayOf  com.poolassistant.pool_assistant  invoke  com.poolassistant.pool_assistant  mapOf  com.poolassistant.pool_assistant  
pollGameState  com.poolassistant.pool_assistant  to  com.poolassistant.pool_assistant  Boolean 4com.poolassistant.pool_assistant.GameDetectorService  Context 4com.poolassistant.pool_assistant.GameDetectorService  Double 4com.poolassistant.pool_assistant.GameDetectorService  	Exception 4com.poolassistant.pool_assistant.GameDetectorService  Int 4com.poolassistant.pool_assistant.GameDetectorService  	JSONArray 4com.poolassistant.pool_assistant.GameDetectorService  
JSONObject 4com.poolassistant.pool_assistant.GameDetectorService  Log 4com.poolassistant.pool_assistant.GameDetectorService  Map 4com.poolassistant.pool_assistant.GameDetectorService  
MethodCall 4com.poolassistant.pool_assistant.GameDetectorService  
MethodChannel 4com.poolassistant.pool_assistant.GameDetectorService  String 4com.poolassistant.pool_assistant.GameDetectorService  TAG 4com.poolassistant.pool_assistant.GameDetectorService  Timer 4com.poolassistant.pool_assistant.GameDetectorService  	TimerTask 4com.poolassistant.pool_assistant.GameDetectorService  createPocket 4com.poolassistant.pool_assistant.GameDetectorService  getColorForBall 4com.poolassistant.pool_assistant.GameDetectorService  getCurrentShotDirection 4com.poolassistant.pool_assistant.GameDetectorService  getCurrentShotPower 4com.poolassistant.pool_assistant.GameDetectorService  getMAPOf 4com.poolassistant.pool_assistant.GameDetectorService  getMapOf 4com.poolassistant.pool_assistant.GameDetectorService  getTO 4com.poolassistant.pool_assistant.GameDetectorService  getTo 4com.poolassistant.pool_assistant.GameDetectorService  
initialize 4com.poolassistant.pool_assistant.GameDetectorService  isDetectionActive 4com.poolassistant.pool_assistant.GameDetectorService  isGameDetected 4com.poolassistant.pool_assistant.GameDetectorService  
isGameRunning 4com.poolassistant.pool_assistant.GameDetectorService  mapOf 4com.poolassistant.pool_assistant.GameDetectorService  
methodChannel 4com.poolassistant.pool_assistant.GameDetectorService  
pollGameState 4com.poolassistant.pool_assistant.GameDetectorService  
sendGameState 4com.poolassistant.pool_assistant.GameDetectorService  startDetection 4com.poolassistant.pool_assistant.GameDetectorService  
stopDetection 4com.poolassistant.pool_assistant.GameDetectorService  timer 4com.poolassistant.pool_assistant.GameDetectorService  to 4com.poolassistant.pool_assistant.GameDetectorService  Boolean >com.poolassistant.pool_assistant.GameDetectorService.Companion  Context >com.poolassistant.pool_assistant.GameDetectorService.Companion  Double >com.poolassistant.pool_assistant.GameDetectorService.Companion  	Exception >com.poolassistant.pool_assistant.GameDetectorService.Companion  Int >com.poolassistant.pool_assistant.GameDetectorService.Companion  	JSONArray >com.poolassistant.pool_assistant.GameDetectorService.Companion  
JSONObject >com.poolassistant.pool_assistant.GameDetectorService.Companion  Log >com.poolassistant.pool_assistant.GameDetectorService.Companion  Map >com.poolassistant.pool_assistant.GameDetectorService.Companion  
MethodCall >com.poolassistant.pool_assistant.GameDetectorService.Companion  
MethodChannel >com.poolassistant.pool_assistant.GameDetectorService.Companion  String >com.poolassistant.pool_assistant.GameDetectorService.Companion  TAG >com.poolassistant.pool_assistant.GameDetectorService.Companion  Timer >com.poolassistant.pool_assistant.GameDetectorService.Companion  	TimerTask >com.poolassistant.pool_assistant.GameDetectorService.Companion  getMAPOf >com.poolassistant.pool_assistant.GameDetectorService.Companion  getMapOf >com.poolassistant.pool_assistant.GameDetectorService.Companion  getTO >com.poolassistant.pool_assistant.GameDetectorService.Companion  getTo >com.poolassistant.pool_assistant.GameDetectorService.Companion  invoke >com.poolassistant.pool_assistant.GameDetectorService.Companion  mapOf >com.poolassistant.pool_assistant.GameDetectorService.Companion  
pollGameState >com.poolassistant.pool_assistant.GameDetectorService.Companion  to >com.poolassistant.pool_assistant.GameDetectorService.Companion  getPOLLGameState Vcom.poolassistant.pool_assistant.GameDetectorService.startDetection.<no name provided>  getPollGameState Vcom.poolassistant.pool_assistant.GameDetectorService.startDetection.<no name provided>  Boolean 1com.poolassistant.pool_assistant.InjectionService  Context 1com.poolassistant.pool_assistant.InjectionService  	Exception 1com.poolassistant.pool_assistant.InjectionService  File 1com.poolassistant.pool_assistant.InjectionService  GAME_PACKAGE_NAME 1com.poolassistant.pool_assistant.InjectionService  Log 1com.poolassistant.pool_assistant.InjectionService  
MethodCall 1com.poolassistant.pool_assistant.InjectionService  
MethodChannel 1com.poolassistant.pool_assistant.InjectionService  Runtime 1com.poolassistant.pool_assistant.InjectionService  TAG 1com.poolassistant.pool_assistant.InjectionService  arrayOf 1com.poolassistant.pool_assistant.InjectionService  checkRootAccess 1com.poolassistant.pool_assistant.InjectionService  context 1com.poolassistant.pool_assistant.InjectionService  fixLoginIssues 1com.poolassistant.pool_assistant.InjectionService  
getARRAYOf 1com.poolassistant.pool_assistant.InjectionService  
getArrayOf 1com.poolassistant.pool_assistant.InjectionService  
initialize 1com.poolassistant.pool_assistant.InjectionService  isGameInstalled 1com.poolassistant.pool_assistant.InjectionService  
isInjected 1com.poolassistant.pool_assistant.InjectionService  
methodChannel 1com.poolassistant.pool_assistant.InjectionService  startInjection 1com.poolassistant.pool_assistant.InjectionService  
stopInjection 1com.poolassistant.pool_assistant.InjectionService  Boolean ;com.poolassistant.pool_assistant.InjectionService.Companion  Context ;com.poolassistant.pool_assistant.InjectionService.Companion  	Exception ;com.poolassistant.pool_assistant.InjectionService.Companion  File ;com.poolassistant.pool_assistant.InjectionService.Companion  GAME_PACKAGE_NAME ;com.poolassistant.pool_assistant.InjectionService.Companion  Log ;com.poolassistant.pool_assistant.InjectionService.Companion  
MethodCall ;com.poolassistant.pool_assistant.InjectionService.Companion  
MethodChannel ;com.poolassistant.pool_assistant.InjectionService.Companion  Runtime ;com.poolassistant.pool_assistant.InjectionService.Companion  TAG ;com.poolassistant.pool_assistant.InjectionService.Companion  arrayOf ;com.poolassistant.pool_assistant.InjectionService.Companion  
getARRAYOf ;com.poolassistant.pool_assistant.InjectionService.Companion  
getArrayOf ;com.poolassistant.pool_assistant.InjectionService.Companion  invoke ;com.poolassistant.pool_assistant.InjectionService.Companion  
FlutterEngine -com.poolassistant.pool_assistant.MainActivity  GAME_DETECTOR_CHANNEL -com.poolassistant.pool_assistant.MainActivity  GameDetectorService -com.poolassistant.pool_assistant.MainActivity  INJECTION_CHANNEL -com.poolassistant.pool_assistant.MainActivity  InjectionService -com.poolassistant.pool_assistant.MainActivity  
MethodChannel -com.poolassistant.pool_assistant.MainActivity  context -com.poolassistant.pool_assistant.MainActivity  gameDetectorService -com.poolassistant.pool_assistant.MainActivity  
getCONTEXT -com.poolassistant.pool_assistant.MainActivity  
getContext -com.poolassistant.pool_assistant.MainActivity  injectionService -com.poolassistant.pool_assistant.MainActivity  invoke -com.poolassistant.pool_assistant.MainActivity  
setContext -com.poolassistant.pool_assistant.MainActivity  FlutterActivity io.flutter.embedding.android  
FlutterEngine ,io.flutter.embedding.android.FlutterActivity  GameDetectorService ,io.flutter.embedding.android.FlutterActivity  InjectionService ,io.flutter.embedding.android.FlutterActivity  
MethodChannel ,io.flutter.embedding.android.FlutterActivity  configureFlutterEngine ,io.flutter.embedding.android.FlutterActivity  invoke ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine io.flutter.embedding.engine  dartExecutor )io.flutter.embedding.engine.FlutterEngine  getDARTExecutor )io.flutter.embedding.engine.FlutterEngine  getDartExecutor )io.flutter.embedding.engine.FlutterEngine  setDartExecutor )io.flutter.embedding.engine.FlutterEngine  binaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  getBINARYMessenger -io.flutter.embedding.engine.dart.DartExecutor  getBinaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  setBinaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  invokeMethod &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  File java.io  IOException java.io  exists java.io.File  	Exception 	java.lang  File 	java.lang  GAME_PACKAGE_NAME 	java.lang  GameDetectorService 	java.lang  InjectionService 	java.lang  	JSONArray 	java.lang  
JSONObject 	java.lang  Log 	java.lang  
MethodChannel 	java.lang  Process 	java.lang  Runtime 	java.lang  TAG 	java.lang  Timer 	java.lang  arrayOf 	java.lang  mapOf 	java.lang  
pollGameState 	java.lang  to 	java.lang  message java.lang.Exception  waitFor java.lang.Process  exec java.lang.Runtime  
getRuntime java.lang.Runtime  Timer 	java.util  	TimerTask 	java.util  cancel java.util.Timer  scheduleAtFixedRate java.util.Timer  
pollGameState java.util.TimerTask  Any kotlin  Array kotlin  Boolean kotlin  Double kotlin  	Exception kotlin  File kotlin  GAME_PACKAGE_NAME kotlin  GameDetectorService kotlin  InjectionService kotlin  Int kotlin  	JSONArray kotlin  
JSONObject kotlin  Log kotlin  Long kotlin  
MethodChannel kotlin  Nothing kotlin  Pair kotlin  Runtime kotlin  String kotlin  TAG kotlin  Timer kotlin  Unit kotlin  arrayOf kotlin  mapOf kotlin  
pollGameState kotlin  to kotlin  getTO 
kotlin.String  getTo 
kotlin.String  	Exception kotlin.annotation  File kotlin.annotation  GAME_PACKAGE_NAME kotlin.annotation  GameDetectorService kotlin.annotation  InjectionService kotlin.annotation  	JSONArray kotlin.annotation  
JSONObject kotlin.annotation  Log kotlin.annotation  
MethodChannel kotlin.annotation  Runtime kotlin.annotation  TAG kotlin.annotation  Timer kotlin.annotation  arrayOf kotlin.annotation  mapOf kotlin.annotation  
pollGameState kotlin.annotation  to kotlin.annotation  	Exception kotlin.collections  File kotlin.collections  GAME_PACKAGE_NAME kotlin.collections  GameDetectorService kotlin.collections  InjectionService kotlin.collections  	JSONArray kotlin.collections  
JSONObject kotlin.collections  Log kotlin.collections  Map kotlin.collections  
MethodChannel kotlin.collections  Runtime kotlin.collections  TAG kotlin.collections  Timer kotlin.collections  arrayOf kotlin.collections  mapOf kotlin.collections  
pollGameState kotlin.collections  to kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  	Exception kotlin.comparisons  File kotlin.comparisons  GAME_PACKAGE_NAME kotlin.comparisons  GameDetectorService kotlin.comparisons  InjectionService kotlin.comparisons  	JSONArray kotlin.comparisons  
JSONObject kotlin.comparisons  Log kotlin.comparisons  
MethodChannel kotlin.comparisons  Runtime kotlin.comparisons  TAG kotlin.comparisons  Timer kotlin.comparisons  arrayOf kotlin.comparisons  mapOf kotlin.comparisons  
pollGameState kotlin.comparisons  to kotlin.comparisons  	Exception 	kotlin.io  File 	kotlin.io  GAME_PACKAGE_NAME 	kotlin.io  GameDetectorService 	kotlin.io  InjectionService 	kotlin.io  	JSONArray 	kotlin.io  
JSONObject 	kotlin.io  Log 	kotlin.io  
MethodChannel 	kotlin.io  Runtime 	kotlin.io  TAG 	kotlin.io  Timer 	kotlin.io  arrayOf 	kotlin.io  mapOf 	kotlin.io  
pollGameState 	kotlin.io  to 	kotlin.io  	Exception 
kotlin.jvm  File 
kotlin.jvm  GAME_PACKAGE_NAME 
kotlin.jvm  GameDetectorService 
kotlin.jvm  InjectionService 
kotlin.jvm  	JSONArray 
kotlin.jvm  
JSONObject 
kotlin.jvm  Log 
kotlin.jvm  
MethodChannel 
kotlin.jvm  Runtime 
kotlin.jvm  TAG 
kotlin.jvm  Timer 
kotlin.jvm  arrayOf 
kotlin.jvm  mapOf 
kotlin.jvm  
pollGameState 
kotlin.jvm  to 
kotlin.jvm  	Exception 
kotlin.ranges  File 
kotlin.ranges  GAME_PACKAGE_NAME 
kotlin.ranges  GameDetectorService 
kotlin.ranges  InjectionService 
kotlin.ranges  IntRange 
kotlin.ranges  	JSONArray 
kotlin.ranges  
JSONObject 
kotlin.ranges  Log 
kotlin.ranges  
MethodChannel 
kotlin.ranges  Runtime 
kotlin.ranges  TAG 
kotlin.ranges  Timer 
kotlin.ranges  arrayOf 
kotlin.ranges  mapOf 
kotlin.ranges  
pollGameState 
kotlin.ranges  to 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  	Exception kotlin.sequences  File kotlin.sequences  GAME_PACKAGE_NAME kotlin.sequences  GameDetectorService kotlin.sequences  InjectionService kotlin.sequences  	JSONArray kotlin.sequences  
JSONObject kotlin.sequences  Log kotlin.sequences  
MethodChannel kotlin.sequences  Runtime kotlin.sequences  TAG kotlin.sequences  Timer kotlin.sequences  arrayOf kotlin.sequences  mapOf kotlin.sequences  
pollGameState kotlin.sequences  to kotlin.sequences  	Exception kotlin.text  File kotlin.text  GAME_PACKAGE_NAME kotlin.text  GameDetectorService kotlin.text  InjectionService kotlin.text  	JSONArray kotlin.text  
JSONObject kotlin.text  Log kotlin.text  
MethodChannel kotlin.text  Runtime kotlin.text  TAG kotlin.text  Timer kotlin.text  arrayOf kotlin.text  mapOf kotlin.text  
pollGameState kotlin.text  to kotlin.text  	JSONArray org.json  
JSONObject org.json  put org.json.JSONArray  put org.json.JSONObject  toString org.json.JSONObject  GameIntegrationService  com.poolassistant.pool_assistant  GameIntegrationService android.app.Activity  Intent android.content  GameIntegrationService android.content.Context  getPACKAGEName android.content.Context  getPackageName android.content.Context  packageName android.content.Context  setPackageName android.content.Context  
startActivity android.content.Context  GameIntegrationService android.content.ContextWrapper  ACTION_VIEW android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  addFlags android.content.Intent  equals android.content.Intent  setDataAndType android.content.Intent  ApplicationInfo android.content.pm  dataDir "android.content.pm.ApplicationInfo  
minSdkVersion "android.content.pm.ApplicationInfo  	sourceDir "android.content.pm.ApplicationInfo  targetSdkVersion "android.content.pm.ApplicationInfo  firstInstallTime android.content.pm.PackageInfo  getLONGVersionCode android.content.pm.PackageInfo  getLongVersionCode android.content.pm.PackageInfo  lastUpdateTime android.content.pm.PackageInfo  longVersionCode android.content.pm.PackageInfo  setLongVersionCode android.content.pm.PackageInfo  versionCode android.content.pm.PackageInfo  versionName android.content.pm.PackageInfo  NameNotFoundException !android.content.pm.PackageManager  getApplicationInfo !android.content.pm.PackageManager  getApplicationLabel !android.content.pm.PackageManager  getLaunchIntentForPackage !android.content.pm.PackageManager  Uri android.net  fromFile android.net.Uri  Build 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  N android.os.Build.VERSION_CODES  P android.os.Build.VERSION_CODES  GameIntegrationService  android.view.ContextThemeWrapper  FileProvider androidx.core.content  
getUriForFile "androidx.core.content.FileProvider  Any  com.poolassistant.pool_assistant  Build  com.poolassistant.pool_assistant  FileProvider  com.poolassistant.pool_assistant  Intent  com.poolassistant.pool_assistant  Suppress  com.poolassistant.pool_assistant  Uri  com.poolassistant.pool_assistant  emptyMap  com.poolassistant.pool_assistant  Any 7com.poolassistant.pool_assistant.GameIntegrationService  Boolean 7com.poolassistant.pool_assistant.GameIntegrationService  Build 7com.poolassistant.pool_assistant.GameIntegrationService  Context 7com.poolassistant.pool_assistant.GameIntegrationService  	Exception 7com.poolassistant.pool_assistant.GameIntegrationService  File 7com.poolassistant.pool_assistant.GameIntegrationService  FileProvider 7com.poolassistant.pool_assistant.GameIntegrationService  Intent 7com.poolassistant.pool_assistant.GameIntegrationService  Map 7com.poolassistant.pool_assistant.GameIntegrationService  
MethodCall 7com.poolassistant.pool_assistant.GameIntegrationService  
MethodChannel 7com.poolassistant.pool_assistant.GameIntegrationService  PackageManager 7com.poolassistant.pool_assistant.GameIntegrationService  String 7com.poolassistant.pool_assistant.GameIntegrationService  Suppress 7com.poolassistant.pool_assistant.GameIntegrationService  Uri 7com.poolassistant.pool_assistant.GameIntegrationService  checkForUpdates 7com.poolassistant.pool_assistant.GameIntegrationService  context 7com.poolassistant.pool_assistant.GameIntegrationService  emptyMap 7com.poolassistant.pool_assistant.GameIntegrationService  
getAppInfo 7com.poolassistant.pool_assistant.GameIntegrationService  getEMPTYMap 7com.poolassistant.pool_assistant.GameIntegrationService  getEmptyMap 7com.poolassistant.pool_assistant.GameIntegrationService  getMAPOf 7com.poolassistant.pool_assistant.GameIntegrationService  getMapOf 7com.poolassistant.pool_assistant.GameIntegrationService  getTO 7com.poolassistant.pool_assistant.GameIntegrationService  getTo 7com.poolassistant.pool_assistant.GameIntegrationService  
installApk 7com.poolassistant.pool_assistant.GameIntegrationService  isAppInstalled 7com.poolassistant.pool_assistant.GameIntegrationService  	launchApp 7com.poolassistant.pool_assistant.GameIntegrationService  mapOf 7com.poolassistant.pool_assistant.GameIntegrationService  registerWith 7com.poolassistant.pool_assistant.GameIntegrationService  to 7com.poolassistant.pool_assistant.GameIntegrationService  Any Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  Boolean Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  Build Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  Context Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  	Exception Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  File Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  FileProvider Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  Intent Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  Map Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  
MethodCall Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  
MethodChannel Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  PackageManager Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  String Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  Suppress Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  Uri Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  emptyMap Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  getEMPTYMap Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  getEmptyMap Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  getMAPOf Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  getMapOf Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  getTO Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  getTo Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  invoke Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  mapOf Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  to Acom.poolassistant.pool_assistant.GameIntegrationService.Companion  GAME_INTEGRATION_CHANNEL -com.poolassistant.pool_assistant.MainActivity  GameIntegrationService -com.poolassistant.pool_assistant.MainActivity  gameIntegrationService -com.poolassistant.pool_assistant.MainActivity  GameIntegrationService ,io.flutter.embedding.android.FlutterActivity  argument #io.flutter.plugin.common.MethodCall  error -io.flutter.plugin.common.MethodChannel.Result  Build 	java.lang  FileProvider 	java.lang  GameIntegrationService 	java.lang  Intent 	java.lang  Uri 	java.lang  emptyMap 	java.lang  Build kotlin  FileProvider kotlin  GameIntegrationService kotlin  Intent kotlin  Suppress kotlin  Uri kotlin  emptyMap kotlin  Build kotlin.annotation  FileProvider kotlin.annotation  GameIntegrationService kotlin.annotation  Intent kotlin.annotation  Uri kotlin.annotation  emptyMap kotlin.annotation  Build kotlin.collections  FileProvider kotlin.collections  GameIntegrationService kotlin.collections  Intent kotlin.collections  Uri kotlin.collections  emptyMap kotlin.collections  Build kotlin.comparisons  FileProvider kotlin.comparisons  GameIntegrationService kotlin.comparisons  Intent kotlin.comparisons  Uri kotlin.comparisons  emptyMap kotlin.comparisons  Build 	kotlin.io  FileProvider 	kotlin.io  GameIntegrationService 	kotlin.io  Intent 	kotlin.io  Uri 	kotlin.io  emptyMap 	kotlin.io  Build 
kotlin.jvm  FileProvider 
kotlin.jvm  GameIntegrationService 
kotlin.jvm  Intent 
kotlin.jvm  Uri 
kotlin.jvm  emptyMap 
kotlin.jvm  Build 
kotlin.ranges  FileProvider 
kotlin.ranges  GameIntegrationService 
kotlin.ranges  Intent 
kotlin.ranges  Uri 
kotlin.ranges  emptyMap 
kotlin.ranges  Build kotlin.sequences  FileProvider kotlin.sequences  GameIntegrationService kotlin.sequences  Intent kotlin.sequences  Uri kotlin.sequences  emptyMap kotlin.sequences  Build kotlin.text  FileProvider kotlin.text  GameIntegrationService kotlin.text  Intent kotlin.text  Uri kotlin.text  emptyMap kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       