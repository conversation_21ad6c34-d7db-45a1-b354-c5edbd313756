[{"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_search_view.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_search_view.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/select_dialog_singlechoice_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/select_dialog_singlechoice_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_template_part_time.xml", "source": "app.meedu.flutter_facebook_auth-core-1.13.1-14:/layout/notification_template_part_time.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_alert_dialog_button_bar_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_alert_dialog_button_bar_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_search_dropdown_item_icons_2line.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_search_dropdown_item_icons_2line.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_list_menu_item_checkbox.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_list_menu_item_checkbox.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/custom_dialog.xml", "source": "app.meedu.flutter_facebook_auth-core-1.13.1-14:/layout/custom_dialog.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_screen_simple.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_screen_simple.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_alert_dialog_title_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_alert_dialog_title_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_action_menu_layout.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_action_menu_layout.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_template_big_media.xml", "source": "app.meedu.flutter_facebook_auth-media-1.0.0-6:/layout/notification_template_big_media.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/support_simple_spinner_dropdown_item.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/support_simple_spinner_dropdown_item.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_action_mode_bar.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_action_mode_bar.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_template_media.xml", "source": "app.meedu.flutter_facebook_auth-media-1.0.0-6:/layout/notification_template_media.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/ime_secondary_split_test_activity.xml", "source": "app.meedu.flutter_facebook_auth-core-1.13.1-14:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_list_menu_item_layout.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_list_menu_item_layout.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_activity_chooser_view_list_item.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_activity_chooser_view_list_item.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/select_dialog_item_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/select_dialog_item_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_dialog_title_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_dialog_title_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_media_action.xml", "source": "app.meedu.flutter_facebook_auth-media-1.0.0-6:/layout/notification_media_action.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/com_facebook_activity_layout.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/layout/com_facebook_activity_layout.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_template_big_media_narrow.xml", "source": "app.meedu.flutter_facebook_auth-media-1.0.0-6:/layout/notification_template_big_media_narrow.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_template_lines_media.xml", "source": "app.meedu.flutter_facebook_auth-media-1.0.0-6:/layout/notification_template_lines_media.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_action_mode_close_item_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_action_mode_close_item_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/com_facebook_device_auth_dialog_fragment.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/layout/com_facebook_device_auth_dialog_fragment.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_template_big_media_narrow_custom.xml", "source": "app.meedu.flutter_facebook_auth-media-1.0.0-6:/layout/notification_template_big_media_narrow_custom.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/com_facebook_tooltip_bubble.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-login-16.3.0-23:/layout/com_facebook_tooltip_bubble.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_template_big_media_custom.xml", "source": "app.meedu.flutter_facebook_auth-media-1.0.0-6:/layout/notification_template_big_media_custom.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_screen_content_include.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_screen_content_include.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/ime_base_split_test_activity.xml", "source": "app.meedu.flutter_facebook_auth-core-1.13.1-14:/layout/ime_base_split_test_activity.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_tooltip.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_tooltip.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_expanded_menu_layout.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_expanded_menu_layout.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_media_cancel_action.xml", "source": "app.meedu.flutter_facebook_auth-media-1.0.0-6:/layout/notification_media_cancel_action.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/com_facebook_login_fragment.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/layout/com_facebook_login_fragment.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_activity_chooser_view.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_activity_chooser_view.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_list_menu_item_radio.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_list_menu_item_radio.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/select_dialog_multichoice_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/select_dialog_multichoice_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_template_part_chronometer.xml", "source": "app.meedu.flutter_facebook_auth-core-1.13.1-14:/layout/notification_template_part_chronometer.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_action_menu_item_layout.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_action_menu_item_layout.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/browser_actions_context_menu_row.xml", "source": "app.meedu.flutter_facebook_auth-browser-1.0.0-3:/layout/browser_actions_context_menu_row.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_popup_menu_item_layout.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_popup_menu_item_layout.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/notification_template_media_custom.xml", "source": "app.meedu.flutter_facebook_auth-media-1.0.0-6:/layout/notification_template_media_custom.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_screen_simple_overlay_action_mode.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_screen_simple_overlay_action_mode.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_select_dialog_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_select_dialog_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_action_bar_up_container.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_action_bar_up_container.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/browser_actions_context_menu_page.xml", "source": "app.meedu.flutter_facebook_auth-browser-1.0.0-3:/layout/browser_actions_context_menu_page.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_popup_menu_header_item_layout.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_popup_menu_header_item_layout.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/com_facebook_smart_device_dialog_fragment.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/layout/com_facebook_smart_device_dialog_fragment.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_alert_dialog_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_alert_dialog_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_cascading_menu_item_layout.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_cascading_menu_item_layout.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_action_bar_title_item.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_action_bar_title_item.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_screen_toolbar.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_screen_toolbar.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/layout/abc_list_menu_item_icon.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/layout/abc_list_menu_item_icon.xml"}]