package com.poolassistant.pool_assistant

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import androidx.core.content.FileProvider
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import java.io.File

class GameIntegrationService(private val context: Context) : MethodChannel.MethodCallHandler {
    
    companion object {
        private const val CHANNEL_NAME = "com.darkpool.assistant/game_integration"
        private const val GAME_PACKAGE_NAME = "com.miniclip.eightballpool"
    }
    
    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "isAppInstalled" -> {
                val packageName = call.argument<String>("packageName")
                if (packageName != null) {
                    result.success(isAppInstalled(packageName))
                } else {
                    result.error("INVALID_ARGUMENT", "Package name is required", null)
                }
            }
            "launchApp" -> {
                val packageName = call.argument<String>("packageName")
                if (packageName != null) {
                    result.success(launchApp(packageName))
                } else {
                    result.error("INVALID_ARGUMENT", "Package name is required", null)
                }
            }
            "installApk" -> {
                val apkPath = call.argument<String>("apkPath")
                if (apkPath != null) {
                    result.success(installApk(apkPath))
                } else {
                    result.error("INVALID_ARGUMENT", "APK path is required", null)
                }
            }
            "getAppInfo" -> {
                val packageName = call.argument<String>("packageName")
                if (packageName != null) {
                    result.success(getAppInfo(packageName))
                } else {
                    result.error("INVALID_ARGUMENT", "Package name is required", null)
                }
            }
            "checkForUpdates" -> {
                val packageName = call.argument<String>("packageName")
                if (packageName != null) {
                    result.success(checkForUpdates(packageName))
                } else {
                    result.error("INVALID_ARGUMENT", "Package name is required", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }
    
    private fun isAppInstalled(packageName: String): Map<String, Any?> {
        return try {
            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            mapOf(
                "installed" to true,
                "version" to packageInfo.versionName,
                "versionCode" to if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode
                } else {
                    @Suppress("DEPRECATION")
                    packageInfo.versionCode.toLong()
                }
            )
        } catch (e: PackageManager.NameNotFoundException) {
            mapOf(
                "installed" to false,
                "version" to null,
                "versionCode" to null
            )
        }
    }
    
    private fun launchApp(packageName: String): Boolean {
        return try {
            val intent = context.packageManager.getLaunchIntentForPackage(packageName)
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }
    
    private fun installApk(apkPath: String): Boolean {
        return try {
            val apkFile = File(apkPath)
            if (!apkFile.exists()) {
                return false
            }
            
            val intent = Intent(Intent.ACTION_VIEW)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val apkUri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    apkFile
                )
                intent.setDataAndType(apkUri, "application/vnd.android.package-archive")
            } else {
                intent.setDataAndType(Uri.fromFile(apkFile), "application/vnd.android.package-archive")
            }
            
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    private fun getAppInfo(packageName: String): Map<String, Any?> {
        return try {
            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            
            mapOf(
                "packageName" to packageName,
                "appName" to packageManager.getApplicationLabel(applicationInfo).toString(),
                "version" to packageInfo.versionName,
                "versionCode" to if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode
                } else {
                    @Suppress("DEPRECATION")
                    packageInfo.versionCode.toLong()
                },
                "installTime" to packageInfo.firstInstallTime,
                "updateTime" to packageInfo.lastUpdateTime,
                "targetSdkVersion" to applicationInfo.targetSdkVersion,
                "minSdkVersion" to if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    applicationInfo.minSdkVersion
                } else {
                    null
                },
                "dataDir" to applicationInfo.dataDir,
                "sourceDir" to applicationInfo.sourceDir
            )
        } catch (e: PackageManager.NameNotFoundException) {
            emptyMap()
        }
    }
    
    private fun checkForUpdates(packageName: String): Boolean {
        // This would typically involve checking with Play Store API
        // For now, we'll return false as this requires more complex implementation
        return false
    }
    
    fun registerWith(channel: MethodChannel) {
        channel.setMethodCallHandler(this)
    }
}
