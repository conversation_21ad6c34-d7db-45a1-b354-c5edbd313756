{"logs": [{"outputFile": "app.meedu.flutter_facebook_auth-release-33:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e820d61ce37e44c44b5b21160e7f3c12\\transformed\\jetified-facebook-login-16.3.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,206,338,477,588,674,764,847,940,1029,1146,1257,1353,1449,1558,1689,1770,1856,2039,2133,2236,2357,2467", "endColumns": "150,131,138,110,85,89,82,92,88,116,110,95,95,108,130,80,85,182,93,102,120,109,161", "endOffsets": "201,333,472,583,669,759,842,935,1024,1141,1252,1348,1444,1553,1684,1765,1851,2034,2128,2231,2352,2462,2624"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3540,3691,3823,3962,4073,4159,4249,4332,4425,4514,4631,4742,4838,4934,5043,5174,5255,5341,5524,5618,5721,5842,5952", "endColumns": "150,131,138,110,85,89,82,92,88,116,110,95,95,108,130,80,85,182,93,102,120,109,161", "endOffsets": "3686,3818,3957,4068,4154,4244,4327,4420,4509,4626,4737,4833,4929,5038,5169,5250,5336,5519,5613,5716,5837,5947,6109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,884,975,1067,1162,1256,1357,1450,1545,1639,1730,1821,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,77,90,91,94,93,100,92,94,93,90,90,79,104,101,97,109,102,108,157,100,80", "endOffsets": "206,304,414,500,602,723,801,879,970,1062,1157,1251,1352,1445,1540,1634,1725,1816,1896,2001,2103,2201,2311,2414,2523,2681,2782,2863"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,884,975,1067,1162,1256,1357,1450,1545,1639,1730,1821,1901,2006,2108,2206,2316,2419,2528,2686,6114", "endColumns": "105,97,109,85,101,120,77,77,90,91,94,93,100,92,94,93,90,90,79,104,101,97,109,102,108,157,100,80", "endOffsets": "206,304,414,500,602,723,801,879,970,1062,1157,1251,1352,1445,1540,1634,1725,1816,1896,2001,2103,2201,2311,2414,2523,2681,2782,6190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "29,30,31,32,33,34,35,60", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2787,2885,2988,3093,3194,3307,3413,6195", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "2880,2983,3088,3189,3302,3408,3535,6291"}}]}, {"outputFile": "app.meedu.flutter_facebook_auth-mergeReleaseResources-31:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e820d61ce37e44c44b5b21160e7f3c12\\transformed\\jetified-facebook-login-16.3.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,206,338,477,588,674,764,847,940,1029,1146,1257,1353,1449,1558,1689,1770,1856,2039,2133,2236,2357,2467", "endColumns": "150,131,138,110,85,89,82,92,88,116,110,95,95,108,130,80,85,182,93,102,120,109,161", "endOffsets": "201,333,472,583,669,759,842,935,1024,1141,1252,1348,1444,1553,1684,1765,1851,2034,2128,2231,2352,2462,2624"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3540,3691,3823,3962,4073,4159,4249,4332,4425,4514,4631,4742,4838,4934,5043,5174,5255,5341,5524,5618,5721,5842,5952", "endColumns": "150,131,138,110,85,89,82,92,88,116,110,95,95,108,130,80,85,182,93,102,120,109,161", "endOffsets": "3686,3818,3957,4068,4154,4244,4327,4420,4509,4626,4737,4833,4929,5038,5169,5250,5336,5519,5613,5716,5837,5947,6109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,884,975,1067,1162,1256,1357,1450,1545,1639,1730,1821,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,77,90,91,94,93,100,92,94,93,90,90,79,104,101,97,109,102,108,157,100,80", "endOffsets": "206,304,414,500,602,723,801,879,970,1062,1157,1251,1352,1445,1540,1634,1725,1816,1896,2001,2103,2201,2311,2414,2523,2681,2782,2863"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,884,975,1067,1162,1256,1357,1450,1545,1639,1730,1821,1901,2006,2108,2206,2316,2419,2528,2686,6114", "endColumns": "105,97,109,85,101,120,77,77,90,91,94,93,100,92,94,93,90,90,79,104,101,97,109,102,108,157,100,80", "endOffsets": "206,304,414,500,602,723,801,879,970,1062,1157,1251,1352,1445,1540,1634,1725,1816,1896,2001,2103,2201,2311,2414,2523,2681,2782,6190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "29,30,31,32,33,34,35,60", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2787,2885,2988,3093,3194,3307,3413,6195", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "2880,2983,3088,3189,3302,3408,3535,6291"}}]}]}