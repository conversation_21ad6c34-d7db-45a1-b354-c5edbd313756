import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import 'services/game_detector.dart';
import 'services/settings_service.dart';
import 'services/subscription_service.dart';
import 'services/activation_service.dart';
import 'services/game_integration_service.dart';
import 'injection/injection_service.dart';
import 'models/admin.dart';
import 'ui/screens/main_screen.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.black,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  // Create services
  final settingsService = SettingsService();
  final gameDetector = GameDetector();
  final injectionService = InjectionService();
  final subscriptionService = SubscriptionService();
  final activationService = ActivationService();
  final gameIntegrationService = GameIntegrationService();

  // Initialize services
  await settingsService.initialize();
  await subscriptionService.initialize();
  await activationService.initialize();
  await gameIntegrationService.initialize();
  await AdminAuth.initialize();

  // Run the app
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider<SettingsService>.value(value: settingsService),
        ChangeNotifierProvider<SubscriptionService>.value(value: subscriptionService),
        ChangeNotifierProvider<ActivationService>.value(value: activationService),
        Provider<GameDetector>.value(value: gameDetector),
        Provider<InjectionService>.value(value: injectionService),
        Provider<GameIntegrationService>.value(value: gameIntegrationService),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '8 Ball Pool Assistant',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
        scaffoldBackgroundColor: Colors.black,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
      ),
      home: const MainScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
