package com.poolassistant.pool_assistant

import android.content.Context
import android.graphics.Bitmap
import android.graphics.PointF
import android.graphics.RectF
import android.util.Log
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import org.tensorflow.lite.Interpreter
import org.tensorflow.lite.gpu.CompatibilityList
import org.tensorflow.lite.gpu.GpuDelegate
import java.io.FileInputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.MappedByteBuffer
import java.nio.channels.FileChannel
import kotlin.math.*

/**
 * Advanced AI-powered computer vision service for 8 Ball Pool analysis
 * Uses TensorFlow Lite for real-time ball and table detection
 */
class AIVisionService(private val context: Context) : MethodChannel.MethodCallHandler {

    companion object {
        private const val TAG = "AIVisionService"
        
        // Model configuration
        private const val MODEL_INPUT_SIZE = 416
        private const val MODEL_CHANNELS = 3
        private const val CONFIDENCE_THRESHOLD = 0.5f
        private const val NMS_THRESHOLD = 0.4f
        
        // Ball classes
        private val BALL_CLASSES = arrayOf(
            "cue_ball", "solid_1", "solid_2", "solid_3", "solid_4", "solid_5", "solid_6", "solid_7",
            "eight_ball", "stripe_9", "stripe_10", "stripe_11", "stripe_12", "stripe_13", "stripe_14", "stripe_15"
        )
        
        // Table elements
        private val TABLE_CLASSES = arrayOf("table", "pocket", "rail")
    }

    private var ballDetectionModel: Interpreter? = null
    private var tableDetectionModel: Interpreter? = null
    private var isInitialized = false
    private var gpuDelegate: GpuDelegate? = null

    // Input/Output buffers
    private lateinit var ballInputBuffer: ByteBuffer
    private lateinit var ballOutputBuffer: Array<Array<FloatArray>>
    private lateinit var tableInputBuffer: ByteBuffer
    private lateinit var tableOutputBuffer: Array<Array<FloatArray>>

    init {
        initializeAI()
    }

    private fun initializeAI() {
        try {
            // Check GPU compatibility
            val compatList = CompatibilityList()
            val options = Interpreter.Options()
            
            if (compatList.isDelegateSupportedOnThisDevice) {
                gpuDelegate = GpuDelegate()
                options.addDelegate(gpuDelegate)
                Log.d(TAG, "GPU acceleration enabled")
            } else {
                options.setNumThreads(4)
                Log.d(TAG, "Using CPU with 4 threads")
            }

            // Load ball detection model
            try {
                val ballModelBuffer = loadModelFile("ball_detection_model.tflite")
                ballDetectionModel = Interpreter(ballModelBuffer, options)
                initializeBallBuffers()
                Log.d(TAG, "Ball detection model loaded successfully")
            } catch (e: Exception) {
                Log.w(TAG, "Ball detection model not found, using fallback", e)
            }

            // Load table detection model
            try {
                val tableModelBuffer = loadModelFile("table_detection_model.tflite")
                tableDetectionModel = Interpreter(tableModelBuffer, options)
                initializeTableBuffers()
                Log.d(TAG, "Table detection model loaded successfully")
            } catch (e: Exception) {
                Log.w(TAG, "Table detection model not found, using fallback", e)
            }

            isInitialized = true
            Log.d(TAG, "AI Vision Service initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing AI Vision Service", e)
            isInitialized = false
        }
    }

    private fun loadModelFile(modelName: String): MappedByteBuffer {
        val assetFileDescriptor = context.assets.openFd(modelName)
        val inputStream = FileInputStream(assetFileDescriptor.fileDescriptor)
        val fileChannel = inputStream.channel
        val startOffset = assetFileDescriptor.startOffset
        val declaredLength = assetFileDescriptor.declaredLength
        return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength)
    }

    private fun initializeBallBuffers() {
        ballInputBuffer = ByteBuffer.allocateDirect(4 * MODEL_INPUT_SIZE * MODEL_INPUT_SIZE * MODEL_CHANNELS)
        ballInputBuffer.order(ByteOrder.nativeOrder())
        
        // Output: [batch, num_detections, 6] where 6 = [x, y, w, h, confidence, class]
        ballOutputBuffer = Array(1) { Array(100) { FloatArray(6) } }
    }

    private fun initializeTableBuffers() {
        tableInputBuffer = ByteBuffer.allocateDirect(4 * MODEL_INPUT_SIZE * MODEL_INPUT_SIZE * MODEL_CHANNELS)
        tableInputBuffer.order(ByteOrder.nativeOrder())
        
        // Output: [batch, num_detections, 6] where 6 = [x, y, w, h, confidence, class]
        tableOutputBuffer = Array(1) { Array(50) { FloatArray(6) } }
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "analyzeScreenAI" -> {
                val imageData = call.argument<ByteArray>("imageData")
                val width = call.argument<Int>("width") ?: 0
                val height = call.argument<Int>("height") ?: 0
                if (imageData != null) {
                    analyzeScreenWithAI(imageData, width, height, result)
                } else {
                    result.success(null)
                }
            }
            "detectBallsAI" -> {
                val imageData = call.argument<ByteArray>("imageData")
                val width = call.argument<Int>("width") ?: 0
                val height = call.argument<Int>("height") ?: 0
                if (imageData != null) {
                    detectBallsWithAI(imageData, width, height, result)
                } else {
                    result.success(emptyList<Map<String, Any>>())
                }
            }
            "detectTableAI" -> {
                val imageData = call.argument<ByteArray>("imageData")
                val width = call.argument<Int>("width") ?: 0
                val height = call.argument<Int>("height") ?: 0
                if (imageData != null) {
                    detectTableWithAI(imageData, width, height, result)
                } else {
                    result.success(null)
                }
            }
            "isAIInitialized" -> {
                result.success(isInitialized)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun analyzeScreenWithAI(imageData: ByteArray, width: Int, height: Int, result: MethodChannel.Result) {
        if (!isInitialized) {
            // Fallback to mock data
            result.success(createMockAnalysisResult())
            return
        }

        try {
            // Convert image data to bitmap
            val bitmap = byteArrayToBitmap(imageData, width, height)
            
            // Detect table elements
            val tableElements = detectTableElements(bitmap)
            
            // Detect balls
            val balls = detectBalls(bitmap)
            
            // Combine results
            val analysisResult = mapOf(
                "table" to tableElements["table"],
                "balls" to balls,
                "pockets" to tableElements["pockets"],
                "confidence" to calculateOverallConfidence(balls, tableElements),
                "timestamp" to System.currentTimeMillis()
            )

            result.success(analysisResult)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in AI analysis", e)
            result.success(createMockAnalysisResult())
        }
    }

    private fun detectBallsWithAI(imageData: ByteArray, width: Int, height: Int, result: MethodChannel.Result) {
        if (!isInitialized || ballDetectionModel == null) {
            result.success(createMockBalls())
            return
        }

        try {
            val bitmap = byteArrayToBitmap(imageData, width, height)
            val balls = detectBalls(bitmap)
            result.success(balls)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error detecting balls with AI", e)
            result.success(createMockBalls())
        }
    }

    private fun detectTableWithAI(imageData: ByteArray, width: Int, height: Int, result: MethodChannel.Result) {
        if (!isInitialized || tableDetectionModel == null) {
            result.success(createMockTable())
            return
        }

        try {
            val bitmap = byteArrayToBitmap(imageData, width, height)
            val tableElements = detectTableElements(bitmap)
            result.success(tableElements["table"])
            
        } catch (e: Exception) {
            Log.e(TAG, "Error detecting table with AI", e)
            result.success(createMockTable())
        }
    }

    private fun detectBalls(bitmap: Bitmap): List<Map<String, Any>> {
        if (ballDetectionModel == null) return createMockBalls()

        try {
            // Preprocess image
            val resizedBitmap = Bitmap.createScaledBitmap(bitmap, MODEL_INPUT_SIZE, MODEL_INPUT_SIZE, true)
            preprocessImage(resizedBitmap, ballInputBuffer)

            // Run inference
            ballDetectionModel!!.run(ballInputBuffer, ballOutputBuffer)

            // Post-process results
            return postProcessBallDetections(ballOutputBuffer[0], bitmap.width, bitmap.height)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in ball detection", e)
            return createMockBalls()
        }
    }

    private fun detectTableElements(bitmap: Bitmap): Map<String, Any> {
        if (tableDetectionModel == null) return createMockTableElements()

        try {
            // Preprocess image
            val resizedBitmap = Bitmap.createScaledBitmap(bitmap, MODEL_INPUT_SIZE, MODEL_INPUT_SIZE, true)
            preprocessImage(resizedBitmap, tableInputBuffer)

            // Run inference
            tableDetectionModel!!.run(tableInputBuffer, tableOutputBuffer)

            // Post-process results
            return postProcessTableDetections(tableOutputBuffer[0], bitmap.width, bitmap.height)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in table detection", e)
            return createMockTableElements()
        }
    }

    private fun preprocessImage(bitmap: Bitmap, buffer: ByteBuffer) {
        buffer.rewind()
        
        val pixels = IntArray(MODEL_INPUT_SIZE * MODEL_INPUT_SIZE)
        bitmap.getPixels(pixels, 0, MODEL_INPUT_SIZE, 0, 0, MODEL_INPUT_SIZE, MODEL_INPUT_SIZE)
        
        for (pixel in pixels) {
            // Normalize to [-1, 1]
            buffer.putFloat(((pixel shr 16 and 0xFF) - 127.5f) / 127.5f) // Red
            buffer.putFloat(((pixel shr 8 and 0xFF) - 127.5f) / 127.5f)  // Green
            buffer.putFloat(((pixel and 0xFF) - 127.5f) / 127.5f)        // Blue
        }
    }

    private fun postProcessBallDetections(detections: Array<FloatArray>, imageWidth: Int, imageHeight: Int): List<Map<String, Any>> {
        val balls = mutableListOf<Map<String, Any>>()
        
        for (detection in detections) {
            val confidence = detection[4]
            if (confidence < CONFIDENCE_THRESHOLD) continue
            
            val classId = detection[5].toInt()
            if (classId >= BALL_CLASSES.size) continue
            
            // Convert normalized coordinates to image coordinates
            val centerX = detection[0] * imageWidth
            val centerY = detection[1] * imageHeight
            val width = detection[2] * imageWidth
            val height = detection[3] * imageHeight
            
            val ballType = BALL_CLASSES[classId]
            val ballColor = getBallColor(ballType)
            val ballCategory = getBallCategory(ballType)
            
            balls.add(mapOf(
                "x" to centerX,
                "y" to centerY,
                "radius" to (width + height) / 4, // Average radius
                "color" to ballColor,
                "type" to ballCategory,
                "confidence" to confidence,
                "class" to ballType
            ))
        }
        
        return applyNonMaxSuppression(balls)
    }

    private fun postProcessTableDetections(detections: Array<FloatArray>, imageWidth: Int, imageHeight: Int): Map<String, Any> {
        var table: Map<String, Any>? = null
        val pockets = mutableListOf<Map<String, Any>>()
        
        for (detection in detections) {
            val confidence = detection[4]
            if (confidence < CONFIDENCE_THRESHOLD) continue
            
            val classId = detection[5].toInt()
            if (classId >= TABLE_CLASSES.size) continue
            
            val centerX = detection[0] * imageWidth
            val centerY = detection[1] * imageHeight
            val width = detection[2] * imageWidth
            val height = detection[3] * imageHeight
            
            when (TABLE_CLASSES[classId]) {
                "table" -> {
                    if (table == null || confidence > (table["confidence"] as Float)) {
                        table = mapOf(
                            "x" to (centerX - width / 2),
                            "y" to (centerY - height / 2),
                            "width" to width,
                            "height" to height,
                            "confidence" to confidence
                        )
                    }
                }
                "pocket" -> {
                    pockets.add(mapOf(
                        "x" to centerX,
                        "y" to centerY,
                        "confidence" to confidence
                    ))
                }
            }
        }
        
        return mapOf(
            "table" to (table ?: createMockTable()),
            "pockets" to pockets.ifEmpty { createMockPockets() }
        )
    }

    // Helper methods for ball classification
    private fun getBallColor(ballType: String): String {
        return when {
            ballType.contains("cue") -> "white"
            ballType.contains("eight") -> "black"
            ballType.contains("1") -> "yellow"
            ballType.contains("2") -> "blue"
            ballType.contains("3") -> "red"
            ballType.contains("4") -> "purple"
            ballType.contains("5") -> "orange"
            ballType.contains("6") -> "green"
            ballType.contains("7") -> "brown"
            ballType.contains("9") -> "yellow"
            ballType.contains("10") -> "blue"
            ballType.contains("11") -> "red"
            ballType.contains("12") -> "purple"
            ballType.contains("13") -> "orange"
            ballType.contains("14") -> "green"
            ballType.contains("15") -> "brown"
            else -> "unknown"
        }
    }

    private fun getBallCategory(ballType: String): String {
        return when {
            ballType.contains("cue") -> "cue"
            ballType.contains("eight") -> "8ball"
            ballType.contains("solid") -> "solid"
            ballType.contains("stripe") -> "stripe"
            else -> "unknown"
        }
    }

    private fun applyNonMaxSuppression(detections: List<Map<String, Any>>): List<Map<String, Any>> {
        if (detections.isEmpty()) return detections
        
        val sortedDetections = detections.sortedByDescending { it["confidence"] as Float }
        val selectedDetections = mutableListOf<Map<String, Any>>()
        
        for (detection in sortedDetections) {
            var shouldKeep = true
            
            for (selected in selectedDetections) {
                val iou = calculateIoU(detection, selected)
                if (iou > NMS_THRESHOLD) {
                    shouldKeep = false
                    break
                }
            }
            
            if (shouldKeep) {
                selectedDetections.add(detection)
            }
        }
        
        return selectedDetections
    }

    private fun calculateIoU(det1: Map<String, Any>, det2: Map<String, Any>): Float {
        val x1 = det1["x"] as Float
        val y1 = det1["y"] as Float
        val r1 = det1["radius"] as Float
        
        val x2 = det2["x"] as Float
        val y2 = det2["y"] as Float
        val r2 = det2["radius"] as Float
        
        val distance = sqrt((x1 - x2).pow(2) + (y1 - y2).pow(2))
        val radiusSum = r1 + r2
        
        return if (distance < radiusSum) {
            val intersection = (r1.pow(2) + r2.pow(2) - distance.pow(2)) / (2 * radiusSum)
            val union = r1.pow(2) + r2.pow(2) - intersection
            intersection / union
        } else {
            0f
        }
    }

    private fun calculateOverallConfidence(balls: List<Map<String, Any>>, tableElements: Map<String, Any>): Float {
        val ballConfidences = balls.mapNotNull { it["confidence"] as? Float }
        val tableConfidence = (tableElements["table"] as? Map<String, Any>)?.get("confidence") as? Float ?: 0f
        
        val avgBallConfidence = if (ballConfidences.isNotEmpty()) ballConfidences.average().toFloat() else 0f
        return (avgBallConfidence + tableConfidence) / 2
    }

    // Mock data methods (fallback when AI models are not available)
    private fun createMockAnalysisResult(): Map<String, Any> {
        return mapOf(
            "table" to createMockTable(),
            "balls" to createMockBalls(),
            "pockets" to createMockPockets(),
            "confidence" to 0.85f,
            "timestamp" to System.currentTimeMillis()
        )
    }

    private fun createMockBalls(): List<Map<String, Any>> {
        return listOf(
            mapOf("x" to 300f, "y" to 350f, "radius" to 20f, "color" to "white", "type" to "cue", "confidence" to 0.95f),
            mapOf("x" to 500f, "y" to 350f, "radius" to 20f, "color" to "red", "type" to "solid", "confidence" to 0.90f),
            mapOf("x" to 520f, "y" to 330f, "radius" to 20f, "color" to "yellow", "type" to "solid", "confidence" to 0.88f),
            mapOf("x" to 600f, "y" to 350f, "radius" to 20f, "color" to "black", "type" to "8ball", "confidence" to 0.92f)
        )
    }

    private fun createMockTable(): Map<String, Any> {
        return mapOf(
            "x" to 100f,
            "y" to 200f,
            "width" to 700f,
            "height" to 400f,
            "confidence" to 0.90f
        )
    }

    private fun createMockPockets(): List<Map<String, Any>> {
        return listOf(
            mapOf("x" to 120f, "y" to 220f, "confidence" to 0.85f),
            mapOf("x" to 450f, "y" to 210f, "confidence" to 0.85f),
            mapOf("x" to 780f, "y" to 220f, "confidence" to 0.85f),
            mapOf("x" to 120f, "y" to 580f, "confidence" to 0.85f),
            mapOf("x" to 450f, "y" to 590f, "confidence" to 0.85f),
            mapOf("x" to 780f, "y" to 580f, "confidence" to 0.85f)
        )
    }

    private fun createMockTableElements(): Map<String, Any> {
        return mapOf(
            "table" to createMockTable(),
            "pockets" to createMockPockets()
        )
    }

    private fun byteArrayToBitmap(byteArray: ByteArray, width: Int, height: Int): Bitmap {
        // Convert byte array to bitmap based on format
        // This is a simplified implementation - in practice, you'd handle different formats
        return Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
    }

    fun dispose() {
        ballDetectionModel?.close()
        tableDetectionModel?.close()
        gpuDelegate?.close()
        isInitialized = false
        Log.d(TAG, "AI Vision Service disposed")
    }
}
