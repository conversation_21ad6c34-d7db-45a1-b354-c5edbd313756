import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Advanced shot analysis panel with AI-powered recommendations
class ShotAnalysisPanel extends StatefulWidget {
  const ShotAnalysisPanel({super.key});

  @override
  State<ShotAnalysisPanel> createState() => _ShotAnalysisPanelState();
}

class _ShotAnalysisPanelState extends State<ShotAnalysisPanel>
    with TickerProviderStateMixin {
  
  late AnimationController _analysisController;
  late AnimationController _radarController;
  late Animation<double> _analysisAnimation;
  late Animation<double> _radarAnimation;
  
  // Analysis state
  bool _isAnalyzing = false;
  ShotAnalysis? _currentAnalysis;
  List<ShotRecommendation> _recommendations = [];
  
  // Selected shot type
  ShotType _selectedShotType = ShotType.straight;

  @override
  void initState() {
    super.initState();
    
    _analysisController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _radarController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
    
    _analysisAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _analysisController,
      curve: Curves.easeInOut,
    ));
    
    _radarAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _radarController,
      curve: Curves.linear,
    ));
    
    // Generate mock recommendations
    _generateMockRecommendations();
  }

  @override
  void dispose() {
    _analysisController.dispose();
    _radarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1B263B).withValues(alpha: 0.95),
            const Color(0xFF0D1B2A).withValues(alpha: 0.95),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFFFF9800).withValues(alpha: 0.5),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFF9800).withValues(alpha: 0.2),
            blurRadius: 20,
            spreadRadius: 3,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 20),
          _buildShotTypeSelector(),
          const SizedBox(height: 20),
          _buildAnalysisControls(),
          if (_currentAnalysis != null) ...[
            const SizedBox(height: 20),
            _buildAnalysisResults(),
          ],
          if (_recommendations.isNotEmpty) ...[
            const SizedBox(height: 20),
            _buildRecommendations(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFFF9800), Color(0xFFF57C00)],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.analytics,
            color: Colors.white,
            size: 28,
          ),
        ),
        const SizedBox(width: 16),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Shot Analysis Engine',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
              ),
              SizedBox(height: 4),
              Text(
                'AI-powered shot optimization',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        AnimatedBuilder(
          animation: _radarAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _radarAnimation.value,
              child: Icon(
                Icons.radar,
                color: _isAnalyzing 
                  ? const Color(0xFFFF9800) 
                  : Colors.white70,
                size: 24,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildShotTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Shot Type',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: ShotType.values.map((type) {
              final isSelected = type == _selectedShotType;
              return Padding(
                padding: const EdgeInsets.only(right: 12),
                child: GestureDetector(
                  onTap: () => setState(() => _selectedShotType = type),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                    decoration: BoxDecoration(
                      gradient: isSelected
                        ? const LinearGradient(
                            colors: [Color(0xFFFF9800), Color(0xFFF57C00)],
                          )
                        : null,
                      color: isSelected ? null : Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected 
                          ? const Color(0xFFFF9800)
                          : Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getShotTypeIcon(type),
                          color: isSelected ? Colors.white : Colors.white70,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          _getShotTypeName(type),
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.white70,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildAnalysisControls() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isAnalyzing ? null : _startAnalysis,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF9800),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 8,
            ),
            icon: _isAnalyzing
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Icon(Icons.search, size: 24),
            label: Text(
              _isAnalyzing ? 'Analyzing...' : 'Analyze Shot',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _clearAnalysis,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey.withValues(alpha: 0.3),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 8,
          ),
          icon: const Icon(Icons.clear, size: 24),
          label: const Text(
            'Clear',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAnalysisResults() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF0D1B2A).withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFFF9800).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.assessment,
                color: Color(0xFFFF9800),
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Analysis Results',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Analysis metrics
          Row(
            children: [
              Expanded(
                child: _buildAnalysisMetric(
                  'Success Rate',
                  '${(_currentAnalysis!.successProbability * 100).toInt()}%',
                  Icons.check_circle,
                  _getSuccessColor(_currentAnalysis!.successProbability),
                ),
              ),
              Expanded(
                child: _buildAnalysisMetric(
                  'Difficulty',
                  _getDifficultyText(_currentAnalysis!.difficulty),
                  Icons.star,
                  _getDifficultyColor(_currentAnalysis!.difficulty),
                ),
              ),
              Expanded(
                child: _buildAnalysisMetric(
                  'Risk Level',
                  _getRiskText(_currentAnalysis!.riskLevel),
                  Icons.warning,
                  _getRiskColor(_currentAnalysis!.riskLevel),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Shot visualization
          Container(
            height: 120,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: CustomPaint(
              painter: ShotVisualizationPainter(_currentAnalysis!),
              size: const Size.fromHeight(120),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisMetric(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildRecommendations() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(
              Icons.lightbulb,
              color: Color(0xFFFF9800),
              size: 20,
            ),
            SizedBox(width: 8),
            Text(
              'AI Recommendations',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        ...(_recommendations.take(3).map((recommendation) => 
          _buildRecommendationCard(recommendation)
        ).toList()),
      ],
    );
  }

  Widget _buildRecommendationCard(ShotRecommendation recommendation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFFFF9800).withValues(alpha: 0.1),
            const Color(0xFFF57C00).withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFFF9800).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getConfidenceColor(recommendation.confidence),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${(recommendation.confidence * 100).toInt()}%',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  recommendation.description,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Row(
            children: [
              _buildRecommendationDetail('Power', '${recommendation.power.toInt()}%'),
              const SizedBox(width: 16),
              _buildRecommendationDetail('Angle', '${recommendation.angle.toInt()}°'),
              const SizedBox(width: 16),
              _buildRecommendationDetail('Spin', recommendation.spinType),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 10,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  // Helper methods
  IconData _getShotTypeIcon(ShotType type) {
    switch (type) {
      case ShotType.straight:
        return Icons.arrow_forward;
      case ShotType.bank:
        return Icons.call_made;
      case ShotType.combination:
        return Icons.scatter_plot;
      case ShotType.jump:
        return Icons.keyboard_arrow_up;
      case ShotType.masse:
        return Icons.rotate_right;
    }
  }

  String _getShotTypeName(ShotType type) {
    switch (type) {
      case ShotType.straight:
        return 'Straight';
      case ShotType.bank:
        return 'Bank';
      case ShotType.combination:
        return 'Combo';
      case ShotType.jump:
        return 'Jump';
      case ShotType.masse:
        return 'Massé';
    }
  }

  Color _getSuccessColor(double probability) {
    if (probability > 0.8) return const Color(0xFF4CAF50);
    if (probability > 0.6) return const Color(0xFFFF9800);
    return const Color(0xFFFF5722);
  }

  Color _getDifficultyColor(ShotDifficulty difficulty) {
    switch (difficulty) {
      case ShotDifficulty.easy:
        return const Color(0xFF4CAF50);
      case ShotDifficulty.medium:
        return const Color(0xFFFF9800);
      case ShotDifficulty.hard:
        return const Color(0xFFFF5722);
      case ShotDifficulty.expert:
        return const Color(0xFF9C27B0);
      case ShotDifficulty.impossible:
        return const Color(0xFF607D8B);
    }
  }

  Color _getRiskColor(RiskLevel risk) {
    switch (risk) {
      case RiskLevel.low:
        return const Color(0xFF4CAF50);
      case RiskLevel.medium:
        return const Color(0xFFFF9800);
      case RiskLevel.high:
        return const Color(0xFFFF5722);
    }
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence > 0.8) return const Color(0xFF4CAF50);
    if (confidence > 0.6) return const Color(0xFFFF9800);
    return const Color(0xFFFF5722);
  }

  String _getDifficultyText(ShotDifficulty difficulty) {
    switch (difficulty) {
      case ShotDifficulty.easy:
        return 'Easy';
      case ShotDifficulty.medium:
        return 'Medium';
      case ShotDifficulty.hard:
        return 'Hard';
      case ShotDifficulty.expert:
        return 'Expert';
      case ShotDifficulty.impossible:
        return 'Impossible';
    }
  }

  String _getRiskText(RiskLevel risk) {
    switch (risk) {
      case RiskLevel.low:
        return 'Low';
      case RiskLevel.medium:
        return 'Medium';
      case RiskLevel.high:
        return 'High';
    }
  }

  void _startAnalysis() async {
    setState(() {
      _isAnalyzing = true;
    });
    
    _analysisController.forward();
    
    // Simulate analysis delay
    await Future.delayed(const Duration(milliseconds: 2000));
    
    // Generate mock analysis
    setState(() {
      _currentAnalysis = ShotAnalysis(
        shotType: _selectedShotType,
        successProbability: 0.75 + math.Random().nextDouble() * 0.2,
        difficulty: ShotDifficulty.values[math.Random().nextInt(ShotDifficulty.values.length)],
        riskLevel: RiskLevel.values[math.Random().nextInt(RiskLevel.values.length)],
        trajectoryPoints: _generateMockTrajectory(),
      );
      _isAnalyzing = false;
    });
    
    _analysisController.reset();
  }

  void _clearAnalysis() {
    setState(() {
      _currentAnalysis = null;
    });
  }

  void _generateMockRecommendations() {
    _recommendations = [
      ShotRecommendation(
        description: 'Straight shot with medium power',
        confidence: 0.85,
        power: 65.0,
        angle: 15.0,
        spinType: 'None',
      ),
      ShotRecommendation(
        description: 'Bank shot off right rail',
        confidence: 0.72,
        power: 80.0,
        angle: 45.0,
        spinType: 'Top',
      ),
      ShotRecommendation(
        description: 'Combination shot through cluster',
        confidence: 0.58,
        power: 55.0,
        angle: -20.0,
        spinType: 'Side',
      ),
    ];
  }

  List<Offset> _generateMockTrajectory() {
    final points = <Offset>[];
    for (int i = 0; i < 20; i++) {
      points.add(Offset(
        i * 10.0,
        50 + math.sin(i * 0.3) * 20,
      ));
    }
    return points;
  }
}

// Data classes
enum ShotType { straight, bank, combination, jump, masse }
enum ShotDifficulty { easy, medium, hard, expert, impossible }
enum RiskLevel { low, medium, high }

class ShotAnalysis {
  final ShotType shotType;
  final double successProbability;
  final ShotDifficulty difficulty;
  final RiskLevel riskLevel;
  final List<Offset> trajectoryPoints;

  ShotAnalysis({
    required this.shotType,
    required this.successProbability,
    required this.difficulty,
    required this.riskLevel,
    required this.trajectoryPoints,
  });
}

class ShotRecommendation {
  final String description;
  final double confidence;
  final double power;
  final double angle;
  final String spinType;

  ShotRecommendation({
    required this.description,
    required this.confidence,
    required this.power,
    required this.angle,
    required this.spinType,
  });
}

/// Custom painter for shot visualization
class ShotVisualizationPainter extends CustomPainter {
  final ShotAnalysis analysis;

  ShotVisualizationPainter(this.analysis);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFFF9800)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    if (analysis.trajectoryPoints.isEmpty) return;

    final path = Path();
    path.moveTo(analysis.trajectoryPoints.first.dx, analysis.trajectoryPoints.first.dy);

    for (int i = 1; i < analysis.trajectoryPoints.length; i++) {
      path.lineTo(analysis.trajectoryPoints[i].dx, analysis.trajectoryPoints[i].dy);
    }

    canvas.drawPath(path, paint);

    // Draw start and end points
    final startPaint = Paint()
      ..color = const Color(0xFF4CAF50)
      ..style = PaintingStyle.fill;

    final endPaint = Paint()
      ..color = const Color(0xFFFF5722)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(analysis.trajectoryPoints.first, 4.0, startPaint);
    canvas.drawCircle(analysis.trajectoryPoints.last, 4.0, endPaint);
  }

  @override
  bool shouldRepaint(ShotVisualizationPainter oldDelegate) {
    return oldDelegate.analysis != analysis;
  }
}
