[{"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_ic_menu_overflow_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_ic_menu_overflow_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_ic_search_api_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_ic_search_api_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_ic_clear_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_ic_clear_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/tooltip_frame_dark.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/tooltip_frame_dark.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_btn_check_material_anim.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_btn_check_material_anim.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_ratingbar_small_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_ratingbar_small_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_list_selector_background_transition_holo_dark.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_list_selector_background_transition_holo_dark.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_ic_arrow_drop_right_black_24dp.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_ic_arrow_drop_right_black_24dp.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_item_background_holo_dark.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_item_background_holo_dark.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/notification_icon_background.xml", "source": "app.meedu.flutter_facebook_auth-core-1.13.1-14:/drawable/notification_icon_background.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_btn_borderless_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_btn_borderless_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_item_background_holo_light.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_item_background_holo_light.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/tooltip_frame_light.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/tooltip_frame_light.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/btn_radio_on_to_off_mtrl_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/btn_radio_on_to_off_mtrl_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/com_facebook_favicon_blue.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/drawable/com_facebook_favicon_blue.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_tab_indicator_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_tab_indicator_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_ic_voice_search_api_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_ic_voice_search_api_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/btn_checkbox_unchecked_mtrl.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/btn_checkbox_unchecked_mtrl.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_seekbar_track_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_seekbar_track_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/com_facebook_button_icon.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/drawable/com_facebook_button_icon.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_spinner_textfield_background_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_spinner_textfield_background_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/btn_radio_off_to_on_mtrl_animation.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/btn_radio_off_to_on_mtrl_animation.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/com_facebook_auth_dialog_background.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/drawable/com_facebook_auth_dialog_background.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_list_selector_background_transition_holo_light.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_list_selector_background_transition_holo_light.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_ic_ab_back_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_ic_ab_back_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/com_facebook_auth_dialog_cancel_background.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/drawable/com_facebook_auth_dialog_cancel_background.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_ic_go_search_api_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_ic_go_search_api_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/btn_radio_on_mtrl.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/btn_radio_on_mtrl.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_btn_radio_material_anim.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_btn_radio_material_anim.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_list_selector_holo_dark.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_list_selector_holo_dark.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_text_cursor_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_text_cursor_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/notification_tile_bg.xml", "source": "app.meedu.flutter_facebook_auth-core-1.13.1-14:/drawable/notification_tile_bg.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_btn_check_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_btn_check_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_btn_radio_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_btn_radio_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_seekbar_tick_mark_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_seekbar_tick_mark_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_ratingbar_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_ratingbar_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_btn_default_mtrl_shape.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_btn_default_mtrl_shape.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/notification_bg_low.xml", "source": "app.meedu.flutter_facebook_auth-core-1.13.1-14:/drawable/notification_bg_low.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_switch_thumb_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_switch_thumb_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_cab_background_top_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_cab_background_top_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/com_facebook_auth_dialog_header_background.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/drawable/com_facebook_auth_dialog_header_background.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/com_facebook_button_like_background.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/drawable/com_facebook_button_like_background.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_seekbar_thumb_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_seekbar_thumb_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_ratingbar_indicator_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_ratingbar_indicator_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/btn_radio_off_mtrl.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/btn_radio_off_mtrl.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_textfield_search_material.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_textfield_search_material.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_cab_background_internal_bg.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_cab_background_internal_bg.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/com_facebook_button_background.xml", "source": "app.meedu.flutter_facebook_auth-jetified-facebook-common-16.3.0-13:/drawable/com_facebook_button_background.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_list_selector_holo_light.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/abc_list_selector_holo_light.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/abc_vector_test.xml", "source": "app.meedu.flutter_facebook_auth-jetified-appcompat-resources-1.1.0-4:/drawable/abc_vector_test.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/btn_checkbox_checked_mtrl.xml", "source": "app.meedu.flutter_facebook_auth-appcompat-1.1.0-5:/drawable/btn_checkbox_checked_mtrl.xml"}, {"merged": "app.meedu.flutter_facebook_auth-release-33:/drawable/notification_bg.xml", "source": "app.meedu.flutter_facebook_auth-core-1.13.1-14:/drawable/notification_bg.xml"}]