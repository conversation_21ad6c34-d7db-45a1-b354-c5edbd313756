import 'dart:ui' as ui;
import 'dart:math' as math;
import 'package:flutter/material.dart' hide Colors;
import 'package:vector_math/vector_math.dart';
import '../models/ball.dart';
import '../models/table.dart';
import '../models/pocket.dart';
import '../models/settings.dart';

/// Service responsible for rendering overlays on top of the game
class OverlayRenderer {
  /// The pool table to render
  final PoolTable table;

  /// Application settings
  final AppSettings settings;

  /// Map of ball IDs to their predicted trajectories
  final Map<int, List<Vector2>> trajectories;

  /// Canvas to draw on
  final Canvas canvas;

  /// Size of the drawing area
  final Size size;

  OverlayRenderer({
    required this.table,
    required this.settings,
    required this.trajectories,
    required this.canvas,
    required this.size,
  });

  /// Renders all overlays
  void render() {
    if (settings.enableStreamingMode) {
      // Don't render anything in streaming mode
      return;
    }

    // Apply calibration transformations
    canvas.save();
    canvas.translate(settings.calibrationOffsetX, settings.calibrationOffsetY);
    canvas.scale(settings.calibrationScale);

    // Render trajectory lines
    if (settings.showTrajectoryLines) {
      _renderTrajectoryLines();
    }

    // Render pocket highlights
    if (settings.showPocketHighlights) {
      _renderPocketHighlights();
    }

    // Restore canvas state
    canvas.restore();
  }

  /// Renders trajectory lines for balls
  void _renderTrajectoryLines() {
    // Find the cue ball
    Ball? cueBall;
    try {
      cueBall = table.balls.firstWhere(
        (ball) => ball.type == BallType.cue && !ball.isPocketed,
      );
    } catch (e) {
      // No cue ball found
      return;
    }

    // Render cue ball trajectory
    if (trajectories.containsKey(cueBall.id)) {
      final trajectory = trajectories[cueBall.id]!;
      if (trajectory.length > 1) {
        _drawDashedLine(
          trajectory,
          settings.cueBallLineColor,
          settings.lineThickness,
        );
      }
    }

    // Render object ball trajectories
    for (final ball in table.balls) {
      if (ball.type != BallType.cue && !ball.isPocketed && trajectories.containsKey(ball.id)) {
        final trajectory = trajectories[ball.id]!;
        if (trajectory.length > 1) {
          _drawDashedLine(
            trajectory,
            settings.objectBallLineColor,
            settings.lineThickness * 0.8,
          );
        }
      }
    }

    // Render collision points
    _renderCollisionPoints();

    // Render aim line from cue stick to cue ball
    _renderAimLine(cueBall);
  }

  /// Renders collision points with visual effects
  void _renderCollisionPoints() {
    for (final entry in trajectories.entries) {
      final trajectory = entry.value;

      // Find collision points (where trajectory changes direction significantly)
      for (int i = 1; i < trajectory.length - 1; i++) {
        final prev = trajectory[i - 1];
        final curr = trajectory[i];
        final next = trajectory[i + 1];

        // Calculate angle change
        final angle1 = math.atan2(curr.y - prev.y, curr.x - prev.x);
        final angle2 = math.atan2(next.y - curr.y, next.x - curr.x);
        final angleDiff = (angle2 - angle1).abs();

        // If significant angle change, mark as collision point
        if (angleDiff > 0.5) {
          _drawCollisionMarker(curr);
        }
      }
    }
  }

  /// Renders aim line from cue stick to cue ball
  void _renderAimLine(Ball cueBall) {
    // Get cue stick position (simplified - would need actual detection)
    final cueStickEnd = Vector2(cueBall.position.x - 100, cueBall.position.y);

    final paint = Paint()
      ..color = settings.aimLineColor
      ..strokeWidth = settings.lineThickness * 1.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Draw line with fade effect
    final gradient = ui.Gradient.linear(
      Offset(cueStickEnd.x, cueStickEnd.y),
      Offset(cueBall.position.x, cueBall.position.y),
      [
        settings.aimLineColor.withOpacity(0.2),
        settings.aimLineColor.withOpacity(0.8),
      ],
    );

    paint.shader = gradient;

    canvas.drawLine(
      Offset(cueStickEnd.x, cueStickEnd.y),
      Offset(cueBall.position.x, cueBall.position.y),
      paint,
    );
  }

  /// Draws a dashed line along the trajectory
  void _drawDashedLine(List<Vector2> trajectory, Color color, double thickness) {
    if (trajectory.length < 2) return;

    final paint = Paint()
      ..color = color
      ..strokeWidth = thickness
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Create dashed path
    final path = Path();
    double distance = 0;
    const dashLength = 10.0;
    const gapLength = 5.0;
    bool isDash = true;

    path.moveTo(trajectory.first.x, trajectory.first.y);

    for (int i = 1; i < trajectory.length; i++) {
      final start = trajectory[i - 1];
      final end = trajectory[i];
      final segmentLength = (end - start).length;

      double segmentDistance = 0;

      while (segmentDistance < segmentLength) {
        final remainingDash = isDash ? dashLength : gapLength;
        final remainingSegment = segmentLength - segmentDistance;
        final stepLength = math.min(remainingDash - (distance % (dashLength + gapLength)), remainingSegment);

        final progress = (segmentDistance + stepLength) / segmentLength;
        final point = start + (end - start) * progress;

        if (isDash) {
          path.lineTo(point.x, point.y);
        } else {
          path.moveTo(point.x, point.y);
        }

        distance += stepLength;
        segmentDistance += stepLength;

        if (distance % (dashLength + gapLength) == 0) {
          isDash = !isDash;
        }
      }
    }

    // Add glow effect
    if (settings.enableGlowEffects) {
      final glowPaint = Paint()
        ..color = color.withOpacity(0.3)
        ..strokeWidth = thickness * 3
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4.0);

      canvas.drawPath(path, glowPaint);
    }

    canvas.drawPath(path, paint);
  }

  /// Draws a collision marker at the specified position
  void _drawCollisionMarker(Vector2 position) {
    final paint = Paint()
      ..color = settings.collisionMarkerColor
      ..style = PaintingStyle.fill;

    // Draw pulsing circle
    final time = DateTime.now().millisecondsSinceEpoch / 1000.0;
    final pulseRadius = 8 + math.sin(time * 4) * 3;

    canvas.drawCircle(
      Offset(position.x, position.y),
      pulseRadius,
      paint,
    );

    // Draw inner circle
    paint.color = Colors.white;
    canvas.drawCircle(
      Offset(position.x, position.y),
      pulseRadius * 0.4,
      paint,
    );
  }

  /// Renders pocket highlights
  void _renderPocketHighlights() {
    for (final pocket in table.pockets) {
      if (pocket.isHighlighted) {
        // Draw a glowing circle around the pocket
        final paint = Paint()
          ..color = settings.pocketHighlightColor.withAlpha(179) // 0.7 * 255 = 179
          ..style = PaintingStyle.stroke
          ..strokeWidth = settings.lineThickness * 2;

        canvas.drawCircle(
          Offset(pocket.position.x, pocket.position.y),
          pocket.radius * 1.2,
          paint,
        );

        // Draw a second, more transparent circle for glow effect
        final glowPaint = Paint()
          ..color = settings.pocketHighlightColor.withAlpha(77) // 0.3 * 255 = 77
          ..style = PaintingStyle.stroke
          ..strokeWidth = settings.lineThickness;

        canvas.drawCircle(
          Offset(pocket.position.x, pocket.position.y),
          pocket.radius * 1.5,
          glowPaint,
        );

        // Draw success probability text if high enough
        if (pocket.successProbability > 0.3) {
          final textStyle = ui.TextStyle(
            color: const ui.Color(0xFFFFFFFF),
            fontSize: 12,
          );

          final paragraphStyle = ui.ParagraphStyle(
            textAlign: TextAlign.center,
          );

          final paragraphBuilder = ui.ParagraphBuilder(paragraphStyle)
            ..pushStyle(textStyle)
            ..addText('${(pocket.successProbability * 100).toInt()}%');

          final paragraph = paragraphBuilder.build();
          paragraph.layout(ui.ParagraphConstraints(width: 50));

          canvas.drawParagraph(
            paragraph,
            Offset(
              pocket.position.x - 25,
              pocket.position.y - pocket.radius * 2,
            ),
          );
        }
      }
    }
  }
}
