import 'dart:async';
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb, debugPrint;
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../services/encryption_service.dart';
import '../services/anti_detection_service.dart';
import 'code_protection_service.dart';

/// Service responsible for injecting the overlay into the 8 Ball Pool game
class InjectionService {
  /// Channel for communicating with native code
  static const MethodChannel _channel = MethodChannel('com.poolassistant/injection');

  /// Whether the injection is currently active
  bool _isInjected = false;

  /// Whether the device has root access
  bool _hasRootAccess = false;

  /// Whether to use no-root mode
  bool _noRootMode = false;

  /// Code protection service
  final CodeProtectionService _codeProtectionService = CodeProtectionService();

  /// Anti-detection service
  final AntiDetectionService _antiDetectionService = AntiDetectionService();

  /// Encryption service
  final EncryptionService _encryptionService = EncryptionService();

  /// Whether the injection is currently active
  bool get isInjected => _isInjected;

  /// Whether the device has root access
  bool get hasRootAccess => _hasRootAccess;

  /// Whether the app is running in no-root mode
  bool get isNoRootMode => _noRootMode;

  /// Whether the environment is safe for injection
  Future<bool> get isSafeEnvironment => _antiDetectionService.isSafeEnvironment();

  /// Initializes the injection service
  Future<void> initialize() async {
    // Initialize protection services
    await _encryptionService.initialize();
    await _antiDetectionService.initialize();
    await _codeProtectionService.initialize();

    // Check if running on web or non-Android platform
    if (kIsWeb || (Platform.isIOS || !(Platform.isAndroid))) {
      // Enable no-root mode for web or testing environments
      _noRootMode = true;
      _hasRootAccess = false;
      debugPrint('Running in no-root mode (web/testing environment)');
      return;
    }

    try {
      // Check for root access
      _hasRootAccess = await _checkRootAccess();

      if (!_hasRootAccess) {
        // Enable no-root mode if root access is not available
        _noRootMode = true;
        debugPrint('Root access not available - enabling no-root mode');
      } else {
        // Check if environment is safe for injection
        final isSafe = await isSafeEnvironment;
        if (!isSafe) {
          debugPrint('Environment is not safe for injection - enabling no-root mode');
          _noRootMode = true;
        } else {
          // Set up method call handler for root mode
          _channel.setMethodCallHandler(_handleMethodCall);
          debugPrint('Root access available - full functionality enabled');
        }
      }
    } catch (e) {
      debugPrint('Failed to initialize injection service: $e - enabling no-root mode');
      _noRootMode = true;
    }
  }

  /// Checks if the device has root access
  Future<bool> _checkRootAccess() async {
    try {
      final result = await _channel.invokeMethod('checkRootAccess');
      return result as bool;
    } catch (e) {
      debugPrint('Error checking root access: $e');
      return false;
    }
  }

  /// Handles method calls from the native side
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onInjectionStatusChanged':
        _isInjected = call.arguments as bool;
        return null;

      default:
        throw PlatformException(
          code: 'Unimplemented',
          details: 'Method ${call.method} not implemented',
        );
    }
  }

  /// Starts the injection process
  Future<bool> startInjection() async {
    // Check if running in no-root mode
    if (_noRootMode) {
      // In no-root mode, we simulate injection by enabling overlay features
      _isInjected = true;
      debugPrint('Started in no-root mode - overlay features enabled');
      return true;
    }

    // Check if running on web or non-Android platform
    if (kIsWeb || (Platform.isIOS || !(Platform.isAndroid))) {
      // Simulate successful injection for web or testing environments
      _isInjected = true;
      return true;
    }

    // Check if environment is safe
    final isSafe = await isSafeEnvironment;
    if (!isSafe) {
      debugPrint('Cannot start injection: Environment is not safe - switching to no-root mode');
      _noRootMode = true;
      _isInjected = true;
      return true;
    }

    if (!_hasRootAccess) {
      debugPrint('Cannot start injection: Root access not available - switching to no-root mode');
      _noRootMode = true;
      _isInjected = true;
      return true;
    }

    try {
      // Request necessary permissions
      final permissionsGranted = await _requestPermissions();
      if (!permissionsGranted) {
        debugPrint('Cannot start injection: Required permissions not granted - switching to no-root mode');
        _noRootMode = true;
        _isInjected = true;
        return true;
      }

      // Prepare protected code for injection
      final trajectoryCode = _codeProtectionService.getCodeSegment('trajectory_prediction');
      final collisionCode = _codeProtectionService.getCodeSegment('collision_detection');
      final pocketCode = _codeProtectionService.getCodeSegment('pocket_detection');
      final autoAimCode = _codeProtectionService.getCodeSegment('auto_aim');

      // Combine code segments
      final injectionCode = {
        'trajectory_prediction': trajectoryCode,
        'collision_detection': collisionCode,
        'pocket_detection': pocketCode,
        'auto_aim': autoAimCode,
      };

      // Start the injection with protected code
      final result = await _channel.invokeMethod('startInjection', injectionCode);
      _isInjected = result as bool;
      return _isInjected;
    } catch (e) {
      debugPrint('Error starting injection: $e - switching to no-root mode');
      _noRootMode = true;
      _isInjected = true;
      return true;
    }
  }

  /// Stops the injection process
  Future<bool> stopInjection() async {
    if (!_isInjected) {
      return true;
    }

    // Check if running on web or non-Android platform
    if (kIsWeb || (Platform.isIOS || !(Platform.isAndroid))) {
      // Simulate successful stopping of injection for web or testing environments
      _isInjected = false;
      return true;
    }

    try {
      final result = await _channel.invokeMethod('stopInjection');
      _isInjected = !(result as bool);
      return !_isInjected;
    } catch (e) {
      debugPrint('Error stopping injection: $e');
      return false;
    }
  }

  /// Requests necessary permissions for the injection
  Future<bool> _requestPermissions() async {
    try {
      // Check Android version
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;

      // Different permissions needed for different Android versions
      final permissions = <Permission>[
        Permission.storage,
      ];

      // Add overlay permission for Android 6.0+
      if (sdkVersion >= 23) {
        permissions.add(Permission.systemAlertWindow);
      }

      // Add accessibility service permission for Android 10+
      if (sdkVersion >= 29) {
        // This permission doesn't exist in the current version of the package
        // We'll need to handle this differently or update the package
      }

      // Request permissions
      final statuses = await permissions.request();

      // Check if all permissions are granted
      return statuses.values.every((status) => status.isGranted);
    } catch (e) {
      debugPrint('Error requesting permissions: $e');
      return false;
    }
  }

  /// Fixes login issues with the game
  Future<bool> fixLoginIssues() async {
    if (!_hasRootAccess || !_isInjected) {
      debugPrint('Cannot fix login issues: Root access or injection not available');
      return false;
    }

    // Check if environment is safe
    final isSafe = await isSafeEnvironment;
    if (!isSafe) {
      debugPrint('Cannot fix login issues: Environment is not safe');
      return false;
    }

    // Check if running on web or non-Android platform
    if (kIsWeb || (Platform.isIOS || !(Platform.isAndroid))) {
      // Simulate successful fixing of login issues for web or testing environments
      return true;
    }

    try {
      final result = await _channel.invokeMethod('fixLoginIssues');
      return result as bool;
    } catch (e) {
      debugPrint('Error fixing login issues: $e');
      return false;
    }
  }

  /// Launches the 8 Ball Pool game without injection
  Future<bool> launchGame() async {
    try {
      // Try to launch the game using different package names
      final packageNames = [
        'com.miniclip.eightballpool', // Official 8 Ball Pool
        'com.miniclip.pool', // Alternative package name
        'com.miniclip.eightball', // Another alternative
      ];

      for (final packageName in packageNames) {
        try {
          if (kIsWeb || (Platform.isIOS || !(Platform.isAndroid))) {
            // For web/testing, just return true
            debugPrint('Game launch simulated for web/testing environment');
            return true;
          }

          // Try to launch the game
          final result = await _channel.invokeMethod('launchGame', packageName);
          if (result == true) {
            debugPrint('Successfully launched game with package: $packageName');
            return true;
          }
        } catch (e) {
          debugPrint('Failed to launch game with package $packageName: $e');
          continue;
        }
      }

      // If all package names failed, try a generic approach
      debugPrint('All specific package names failed, trying generic launch');
      return await _launchGameGeneric();
    } catch (e) {
      debugPrint('Error launching game: $e');
      return false;
    }
  }

  /// Generic method to launch the game
  Future<bool> _launchGameGeneric() async {
    try {
      // For Android, try to open the Play Store page for 8 Ball Pool
      const playStoreUrl = 'market://details?id=com.miniclip.eightballpool';
      const webUrl = 'https://play.google.com/store/apps/details?id=com.miniclip.eightballpool';

      if (kIsWeb || (Platform.isIOS || !(Platform.isAndroid))) {
        debugPrint('Generic game launch simulated');
        return true;
      }

      // Try to open the Play Store
      final result = await _channel.invokeMethod('openUrl', playStoreUrl);
      if (result == true) {
        debugPrint('Opened Play Store page for 8 Ball Pool');
        return true;
      }

      // If Play Store fails, try web URL
      final webResult = await _channel.invokeMethod('openUrl', webUrl);
      if (webResult == true) {
        debugPrint('Opened web page for 8 Ball Pool');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Error in generic game launch: $e');
      return false;
    }
  }

  /// Disposes of resources
  void dispose() {
    if (_isInjected) {
      stopInjection();
    }

    // Dispose of anti-detection service
    _antiDetectionService.dispose();
  }
}
