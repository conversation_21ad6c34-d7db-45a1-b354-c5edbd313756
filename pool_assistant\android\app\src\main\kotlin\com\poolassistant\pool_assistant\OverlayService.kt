package com.poolassistant.pool_assistant

import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.*
import android.os.Build
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.*
import android.widget.Toast
import androidx.annotation.RequiresApi
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

/**
 * Overlay service for drawing aim lines and predictions over the game
 * Uses system-level overlay window with draw over apps permission
 */
@RequiresApi(Build.VERSION_CODES.M)
class OverlayService : Service(), MethodChannel.MethodCallHandler {

    companion object {
        private const val TAG = "OverlayService"
        private var instance: OverlayService? = null

        fun getInstance(): OverlayService? = instance
    }

    private var windowManager: WindowManager? = null
    private var overlayView: OverlayView? = null
    private var isOverlayActive = false

    // Current aim data
    private var currentAimData: AimData? = null

    override fun onCreate() {
        super.onCreate()
        instance = this
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        Log.d(TAG, "OverlayService created")
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            "START_OVERLAY" -> startOverlay()
            "STOP_OVERLAY" -> stopOverlay()
            "UPDATE_OVERLAY" -> {
                val aimData = intent.getParcelableExtra<AimData>("aimData")
                updateOverlay(aimData)
            }
        }
        return START_STICKY
    }

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "startOverlay" -> {
                val success = startOverlay()
                result.success(success)
            }
            "stopOverlay" -> {
                val success = stopOverlay()
                result.success(success)
            }
            "updateOverlay" -> {
                val aimDataMap = call.arguments as? Map<String, Any>
                val aimData = parseAimData(aimDataMap)
                val success = updateOverlay(aimData)
                result.success(success)
            }
            "isOverlayActive" -> {
                result.success(isOverlayActive)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun startOverlay(): Boolean {
        if (isOverlayActive) {
            return true
        }

        try {
            // Check if we have overlay permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
                Toast.makeText(this, "Overlay permission required", Toast.LENGTH_SHORT).show()
                return false
            }

            // Create overlay view
            overlayView = OverlayView(this)

            // Set up window parameters
            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.MATCH_PARENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                },
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                        WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                PixelFormat.TRANSLUCENT
            )

            params.gravity = Gravity.TOP or Gravity.START
            params.x = 0
            params.y = 0

            // Add view to window manager
            windowManager?.addView(overlayView, params)
            isOverlayActive = true

            Log.d(TAG, "Overlay started successfully")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "Error starting overlay", e)
            return false
        }
    }

    private fun stopOverlay(): Boolean {
        try {
            if (overlayView != null && isOverlayActive) {
                windowManager?.removeView(overlayView)
                overlayView = null
                isOverlayActive = false
                Log.d(TAG, "Overlay stopped")
            }
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping overlay", e)
            return false
        }
    }

    private fun updateOverlay(aimData: AimData?): Boolean {
        return try {
            currentAimData = aimData
            overlayView?.updateAimData(aimData)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating overlay", e)
            false
        }
    }

    private fun parseAimData(aimDataMap: Map<String, Any>?): AimData? {
        if (aimDataMap == null) return null

        return try {
            val trajectoryList = aimDataMap["trajectory"] as? List<Map<String, Any>>
            val trajectory = trajectoryList?.map { point ->
                PointF(
                    (point["x"] as? Number)?.toFloat() ?: 0f,
                    (point["y"] as? Number)?.toFloat() ?: 0f
                )
            } ?: emptyList()

            val cueBallMap = aimDataMap["cueBall"] as? Map<String, Any>
            val cueBall = cueBallMap?.let {
                PointF(
                    (it["x"] as? Number)?.toFloat() ?: 0f,
                    (it["y"] as? Number)?.toFloat() ?: 0f
                )
            }

            val targetBallMap = aimDataMap["targetBall"] as? Map<String, Any>
            val targetBall = targetBallMap?.let {
                PointF(
                    (it["x"] as? Number)?.toFloat() ?: 0f,
                    (it["y"] as? Number)?.toFloat() ?: 0f
                )
            }

            val pocketMap = aimDataMap["pocket"] as? Map<String, Any>
            val pocket = pocketMap?.let {
                PointF(
                    (it["x"] as? Number)?.toFloat() ?: 0f,
                    (it["y"] as? Number)?.toFloat() ?: 0f
                )
            }

            AimData(
                trajectory = trajectory,
                cueBall = cueBall,
                targetBall = targetBall,
                pocket = pocket,
                power = (aimDataMap["power"] as? Number)?.toFloat() ?: 0f,
                angle = (aimDataMap["angle"] as? Number)?.toFloat() ?: 0f,
                confidence = (aimDataMap["confidence"] as? Number)?.toFloat() ?: 0f
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing aim data", e)
            null
        }
    }

    override fun onDestroy() {
        stopOverlay()
        instance = null
        super.onDestroy()
    }

    /**
     * Custom view for drawing aim lines and predictions
     */
    private class OverlayView(context: Context) : View(context) {

        private var aimData: AimData? = null

        // Paint objects for different elements
        private val trajectoryPaint = Paint().apply {
            color = Color.RED
            strokeWidth = 4f
            style = Paint.Style.STROKE
            isAntiAlias = true
            pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f)
        }

        private val aimLinePaint = Paint().apply {
            color = Color.CYAN
            strokeWidth = 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
        }

        private val pocketHighlightPaint = Paint().apply {
            color = Color.GREEN
            strokeWidth = 8f
            style = Paint.Style.STROKE
            isAntiAlias = true
        }

        private val powerIndicatorPaint = Paint().apply {
            color = Color.YELLOW
            strokeWidth = 3f
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        fun updateAimData(newAimData: AimData?) {
            aimData = newAimData
            invalidate() // Trigger redraw
        }

        override fun onDraw(canvas: Canvas) {
            super.onDraw(canvas)

            val data = aimData ?: return

            // Draw trajectory line
            if (data.trajectory.isNotEmpty()) {
                drawTrajectory(canvas, data.trajectory)
            }

            // Draw aim line from cue ball to target
            if (data.cueBall != null && data.targetBall != null) {
                drawAimLine(canvas, data.cueBall, data.targetBall)
            }

            // Highlight target pocket
            if (data.pocket != null) {
                drawPocketHighlight(canvas, data.pocket)
            }

            // Draw power indicator
            if (data.cueBall != null) {
                drawPowerIndicator(canvas, data.cueBall, data.power)
            }

            // Draw confidence indicator
            drawConfidenceIndicator(canvas, data.confidence)
        }

        private fun drawTrajectory(canvas: Canvas, trajectory: List<PointF>) {
            if (trajectory.size < 2) return

            val path = Path()
            path.moveTo(trajectory[0].x, trajectory[0].y)

            for (i in 1 until trajectory.size) {
                path.lineTo(trajectory[i].x, trajectory[i].y)
            }

            canvas.drawPath(path, trajectoryPaint)
        }

        private fun drawAimLine(canvas: Canvas, cueBall: PointF, targetBall: PointF) {
            canvas.drawLine(cueBall.x, cueBall.y, targetBall.x, targetBall.y, aimLinePaint)
        }

        private fun drawPocketHighlight(canvas: Canvas, pocket: PointF) {
            canvas.drawCircle(pocket.x, pocket.y, 30f, pocketHighlightPaint)
        }

        private fun drawPowerIndicator(canvas: Canvas, cueBall: PointF, power: Float) {
            val barWidth = 100f
            val barHeight = 10f
            val filledWidth = barWidth * power

            val left = cueBall.x - barWidth / 2
            val top = cueBall.y - 50f

            // Draw power bar background
            canvas.drawRect(left, top, left + barWidth, top + barHeight,
                Paint().apply { color = Color.GRAY; alpha = 128 })

            // Draw filled portion
            canvas.drawRect(left, top, left + filledWidth, top + barHeight, powerIndicatorPaint)
        }

        private fun drawConfidenceIndicator(canvas: Canvas, confidence: Float) {
            val text = "Confidence: ${(confidence * 100).toInt()}%"
            val textPaint = Paint().apply {
                color = Color.WHITE
                textSize = 24f
                isAntiAlias = true
            }

            canvas.drawText(text, 50f, 100f, textPaint)
        }
    }

    /**
     * Data class for aim assistance information
     */
    data class AimData(
        val trajectory: List<PointF>,
        val cueBall: PointF?,
        val targetBall: PointF?,
        val pocket: PointF?,
        val power: Float,
        val angle: Float,
        val confidence: Float
    ) : android.os.Parcelable {
        constructor(parcel: android.os.Parcel) : this(
            trajectory = mutableListOf<PointF>().apply {
                parcel.readTypedList(this, PointF.CREATOR)
            },
            cueBall = parcel.readParcelable(PointF::class.java.classLoader),
            targetBall = parcel.readParcelable(PointF::class.java.classLoader),
            pocket = parcel.readParcelable(PointF::class.java.classLoader),
            power = parcel.readFloat(),
            angle = parcel.readFloat(),
            confidence = parcel.readFloat()
        )

        override fun writeToParcel(parcel: android.os.Parcel, flags: Int) {
            parcel.writeTypedList(trajectory)
            parcel.writeParcelable(cueBall, flags)
            parcel.writeParcelable(targetBall, flags)
            parcel.writeParcelable(pocket, flags)
            parcel.writeFloat(power)
            parcel.writeFloat(angle)
            parcel.writeFloat(confidence)
        }

        override fun describeContents(): Int = 0

        companion object CREATOR : android.os.Parcelable.Creator<AimData> {
            override fun createFromParcel(parcel: android.os.Parcel): AimData = AimData(parcel)
            override fun newArray(size: Int): Array<AimData?> = arrayOfNulls(size)
        }
    }
}
