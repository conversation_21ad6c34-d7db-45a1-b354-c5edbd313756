<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright (c) Meta Platforms, Inc. and affiliates.
    All rights reserved.

    This source code is licensed under the license found in the
    LICENSE file in the root directory of this source tree.
-->

<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/com_facebook_button_corner_radius" />
            <solid android:color="@color/com_facebook_button_background_color_pressed" />
        </shape>
    </item>
    <item android:state_enabled="false"
        android:state_focused="false">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/com_facebook_button_corner_radius" />
            <solid android:color="@color/com_facebook_button_background_color_disabled" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/com_facebook_button_corner_radius" />
            <solid android:color="@color/com_facebook_button_background_color" />
        </shape>
    </item>
</selector>
