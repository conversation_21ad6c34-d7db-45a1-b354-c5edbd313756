-- Project Version: 1.4.1
-- OpenCV Version: 4.11.0+0
-- DartCv Version: ********
-- Android: Targeting API '24' with architecture 'x86', ABI 'x86', and processor 'i686'
-- Android: Selected unified Clang toolchain
-- The CXX compiler identification is Clang 12.0.8
-- Detecting CXX compiler <PERSON><PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Unsupported ABI: x86
-- Configuring done
-- Generating done
-- Build files have been written to: C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/opencv_dart-1.4.1/android/.cxx/Debug/3s5c3p1s/x86
