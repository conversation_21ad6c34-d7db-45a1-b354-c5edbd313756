import 'package:flutter/material.dart';
import 'package:vector_math/vector_math.dart' as vm;
import '../../services/advanced_physics_service.dart';

/// Advanced physics control panel for fine-tuning simulation parameters
class AdvancedPhysicsPanel extends StatefulWidget {
  final AdvancedPhysicsService physicsService;

  const AdvancedPhysicsPanel({
    super.key,
    required this.physicsService,
  });

  @override
  State<AdvancedPhysicsPanel> createState() => _AdvancedPhysicsPanelState();
}

class _AdvancedPhysicsPanelState extends State<AdvancedPhysicsPanel>
    with TickerProviderStateMixin {
  
  late AnimationController _simulationController;
  late Animation<double> _simulationAnimation;
  
  // Physics parameters
  double _power = 5.0;
  double _angle = 0.0;
  double _topSpin = 0.0;
  double _sideSpin = 0.0;
  double _friction = 0.01;
  double _restitution = 0.95;
  
  // Simulation state
  bool _isSimulating = false;
  TrajectoryResult? _currentTrajectory;
  ShotRecommendation? _currentRecommendation;

  @override
  void initState() {
    super.initState();
    
    _simulationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _simulationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _simulationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _simulationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1B263B).withValues(alpha: 0.95),
            const Color(0xFF0D1B2A).withValues(alpha: 0.95),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.5),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
            blurRadius: 20,
            spreadRadius: 3,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 20),
          _buildPhysicsControls(),
          const SizedBox(height: 20),
          _buildSimulationControls(),
          if (_currentTrajectory != null) ...[
            const SizedBox(height: 20),
            _buildTrajectoryVisualization(),
          ],
          if (_currentRecommendation != null) ...[
            const SizedBox(height: 20),
            _buildShotRecommendation(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.science,
            color: Colors.white,
            size: 28,
          ),
        ),
        const SizedBox(width: 16),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Advanced Physics Engine',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
              ),
              SizedBox(height: 4),
              Text(
                'Realistic ball dynamics simulation',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        AnimatedBuilder(
          animation: _simulationAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _simulationAnimation.value * 2 * 3.14159,
              child: Icon(
                Icons.settings,
                color: _isSimulating 
                  ? const Color(0xFF4CAF50) 
                  : Colors.white70,
                size: 24,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPhysicsControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Shot Parameters',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // Power control
        _buildSliderControl(
          'Power',
          _power,
          0.0,
          10.0,
          'N',
          Icons.flash_on,
          (value) => setState(() => _power = value),
        ),
        
        // Angle control
        _buildSliderControl(
          'Angle',
          _angle,
          -180.0,
          180.0,
          '°',
          Icons.rotate_right,
          (value) => setState(() => _angle = value),
        ),
        
        // Top spin control
        _buildSliderControl(
          'Top Spin',
          _topSpin,
          -5.0,
          5.0,
          'rad/s',
          Icons.keyboard_arrow_up,
          (value) => setState(() => _topSpin = value),
        ),
        
        // Side spin control
        _buildSliderControl(
          'Side Spin',
          _sideSpin,
          -3.0,
          3.0,
          'rad/s',
          Icons.keyboard_arrow_right,
          (value) => setState(() => _sideSpin = value),
        ),
        
        const SizedBox(height: 16),
        const Text(
          'Physics Constants',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // Friction control
        _buildSliderControl(
          'Friction',
          _friction,
          0.005,
          0.05,
          '',
          Icons.texture,
          (value) => setState(() => _friction = value),
        ),
        
        // Restitution control
        _buildSliderControl(
          'Restitution',
          _restitution,
          0.7,
          1.0,
          '',
          Icons.sports_tennis,
          (value) => setState(() => _restitution = value),
        ),
      ],
    );
  }

  Widget _buildSliderControl(
    String label,
    double value,
    double min,
    double max,
    String unit,
    IconData icon,
    Function(double) onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: const Color(0xFF4CAF50),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${value.toStringAsFixed(2)} $unit',
                  style: const TextStyle(
                    color: Color(0xFF4CAF50),
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: const Color(0xFF4CAF50),
              inactiveTrackColor: const Color(0xFF4CAF50).withValues(alpha: 0.3),
              thumbColor: const Color(0xFF4CAF50),
              overlayColor: const Color(0xFF4CAF50).withValues(alpha: 0.2),
              trackHeight: 4,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimulationControls() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isSimulating ? null : _runSimulation,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 8,
            ),
            icon: _isSimulating
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Icon(Icons.play_arrow, size: 24),
            label: Text(
              _isSimulating ? 'Simulating...' : 'Run Simulation',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton.icon(
          onPressed: _calculateOptimalShot,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2196F3),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 8,
          ),
          icon: const Icon(Icons.psychology, size: 24),
          label: const Text(
            'AI Suggest',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTrajectoryVisualization() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF0D1B2A).withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.timeline,
                color: Color(0xFF4CAF50),
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Trajectory Analysis',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Trajectory stats
          Row(
            children: [
              Expanded(
                child: _buildTrajectorystat(
                  'Total Time',
                  '${_currentTrajectory!.totalTime.toStringAsFixed(2)}s',
                  Icons.timer,
                ),
              ),
              Expanded(
                child: _buildTrajectorystat(
                  'Collisions',
                  '${_currentTrajectory!.collisions.length}',
                  Icons.sports_baseball,
                ),
              ),
              Expanded(
                child: _buildTrajectorystat(
                  'Distance',
                  '${(_currentTrajectory!.finalPosition - _currentTrajectory!.trajectory.first.position).length.toStringAsFixed(1)}m',
                  Icons.straighten,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Trajectory visualization (simplified)
          Container(
            height: 100,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: CustomPaint(
              painter: TrajectoryPainter(_currentTrajectory!),
              size: const Size.fromHeight(100),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrajectorystat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(0xFF4CAF50),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildShotRecommendation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF2196F3).withValues(alpha: 0.2),
            const Color(0xFF1976D2).withValues(alpha: 0.2),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF2196F3).withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.psychology,
                color: Color(0xFF2196F3),
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'AI Shot Recommendation',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildRecommendationStat(
                  'Confidence',
                  '${(_currentRecommendation!.confidence * 100).toInt()}%',
                  Icons.verified,
                ),
              ),
              Expanded(
                child: _buildRecommendationStat(
                  'Difficulty',
                  _getDifficultyText(_currentRecommendation!.difficulty),
                  Icons.star,
                ),
              ),
              Expanded(
                child: _buildRecommendationStat(
                  'Score',
                  '${_currentRecommendation!.score.toInt()}',
                  Icons.score,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: const Color(0xFF2196F3),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  String _getDifficultyText(ShotDifficulty difficulty) {
    switch (difficulty) {
      case ShotDifficulty.easy:
        return 'Easy';
      case ShotDifficulty.medium:
        return 'Medium';
      case ShotDifficulty.hard:
        return 'Hard';
      case ShotDifficulty.expert:
        return 'Expert';
      case ShotDifficulty.impossible:
        return 'Impossible';
    }
  }

  void _runSimulation() async {
    setState(() {
      _isSimulating = true;
    });
    
    _simulationController.forward();
    
    // Simulate delay for physics calculation
    await Future.delayed(const Duration(milliseconds: 1500));
    
    // Create mock trajectory for demonstration
    final trajectory = widget.physicsService.calculateAdvancedTrajectory(
      startPosition: vm.Vector2(1.0, 1.0),
      initialVelocity: vm.Vector2(_power * 0.1, 0.0),
      initialSpin: vm.Vector3(_topSpin, _sideSpin, 0.0),
      obstacles: [],
      table: TableGeometry(
        bounds: const Rect.fromLTWH(0, 0, 2.84, 1.42),
        pockets: [
          vm.Vector2(0.1, 0.1),
          vm.Vector2(1.42, 0.1),
          vm.Vector2(2.74, 0.1),
          vm.Vector2(0.1, 1.32),
          vm.Vector2(1.42, 1.32),
          vm.Vector2(2.74, 1.32),
        ],
      ),
    );
    
    setState(() {
      _currentTrajectory = trajectory;
      _isSimulating = false;
    });
    
    _simulationController.reset();
  }

  void _calculateOptimalShot() async {
    // Mock optimal shot calculation
    final recommendation = widget.physicsService.calculateOptimalShot(
      cueBall: Ball(
        position: vm.Vector2(1.0, 1.0),
        radius: 0.028575,
        type: 'cue',
        color: 'white',
      ),
      targetBall: Ball(
        position: vm.Vector2(2.0, 1.0),
        radius: 0.028575,
        type: 'solid',
        color: 'red',
      ),
      targetPocket: vm.Vector2(2.74, 0.1),
      obstacles: [],
      table: TableGeometry(
        bounds: const Rect.fromLTWH(0, 0, 2.84, 1.42),
        pockets: [
          vm.Vector2(0.1, 0.1),
          vm.Vector2(1.42, 0.1),
          vm.Vector2(2.74, 0.1),
          vm.Vector2(0.1, 1.32),
          vm.Vector2(1.42, 1.32),
          vm.Vector2(2.74, 1.32),
        ],
      ),
    );
    
    setState(() {
      _currentRecommendation = recommendation;
      // Update controls with recommended values
      _power = recommendation.velocity.length;
      _angle = recommendation.velocity.angleToSigned(vm.Vector2(1, 0)) * 180 / 3.14159;
      _topSpin = recommendation.spin.x;
      _sideSpin = recommendation.spin.y;
    });
  }
}

/// Custom painter for trajectory visualization
class TrajectoryPainter extends CustomPainter {
  final TrajectoryResult trajectory;

  TrajectoryPainter(this.trajectory);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF4CAF50)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final collisionPaint = Paint()
      ..color = const Color(0xFFFF5722)
      ..style = PaintingStyle.fill;

    if (trajectory.trajectory.isEmpty) return;

    // Draw trajectory path
    final path = Path();
    final firstPoint = trajectory.trajectory.first;
    path.moveTo(
      firstPoint.position.x * size.width / 3.0,
      firstPoint.position.y * size.height / 2.0,
    );

    for (int i = 1; i < trajectory.trajectory.length; i++) {
      final point = trajectory.trajectory[i];
      path.lineTo(
        point.position.x * size.width / 3.0,
        point.position.y * size.height / 2.0,
      );
    }

    canvas.drawPath(path, paint);

    // Draw collision points
    for (final collision in trajectory.collisions) {
      canvas.drawCircle(
        Offset(
          collision.position.x * size.width / 3.0,
          collision.position.y * size.height / 2.0,
        ),
        4.0,
        collisionPaint,
      );
    }
  }

  @override
  bool shouldRepaint(TrajectoryPainter oldDelegate) {
    return oldDelegate.trajectory != trajectory;
  }
}
