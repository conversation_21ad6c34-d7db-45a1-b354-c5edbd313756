package com.poolassistant.pool_assistant

import android.content.Intent
import android.os.Build
import androidx.annotation.RequiresApi
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    // Method channel names
    private val INJECTION_CHANNEL = "com.poolassistant/injection"
    private val GAME_DETECTOR_CHANNEL = "com.poolassistant/game_detector"
    private val GAME_INTEGRATION_CHANNEL = "com.darkpool.assistant/game_integration"
    private val SCREEN_CAPTURE_CHANNEL = "com.darkpool.assistant/screen_capture"
    private val OVERLAY_CHANNEL = "com.darkpool.assistant/overlay"
    private val VISION_CHANNEL = "com.darkpool.assistant/vision"

    // Service instances
    // Services will be implemented later

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Basic method channel setup for future implementation
        val basicChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "com.darkpool.assistant/basic")
        basicChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                "getAppInfo" -> {
                    result.success("Dark Pool v1.0.0")
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        // Activity result handling will be implemented later
    }

    override fun onDestroy() {
        super.onDestroy()
        // Cleanup will be implemented later
    }
}
