package com.poolassistant.pool_assistant

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val INJECTION_CHANNEL = "com.poolassistant/injection"
    private val GAME_DETECTOR_CHANNEL = "com.poolassistant/game_detector"
    private val GAME_INTEGRATION_CHANNEL = "com.darkpool.assistant/game_integration"

    private lateinit var injectionService: InjectionService
    private lateinit var gameDetectorService: GameDetectorService
    private lateinit var gameIntegrationService: GameIntegrationService

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Initialize injection service
        injectionService = InjectionService(context)
        val injectionChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, INJECTION_CHANNEL)
        injectionService.initialize(injectionChannel)

        // Initialize game detector service
        gameDetectorService = GameDetectorService(context)
        val gameDetectorChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, GAME_DETECTOR_CHANNEL)
        gameDetectorService.initialize(gameDetectorChannel)

        // Initialize game integration service
        gameIntegrationService = GameIntegrationService(context)
        val gameIntegrationChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, GAME_INTEGRATION_CHANNEL)
        gameIntegrationService.registerWith(gameIntegrationChannel)
    }
}
