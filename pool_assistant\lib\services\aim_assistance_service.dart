import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'game_integration_service.dart';

/// Advanced aim assistance service that coordinates computer vision,
/// physics calculations, and overlay rendering for educational purposes
class AimAssistanceService extends ChangeNotifier {
  static const MethodChannel _physicsChannel = MethodChannel('com.darkpool.assistant/physics');

  final GameIntegrationService _gameIntegrationService;

  // State variables
  bool _isActive = false;
  bool _isAnalyzing = false;
  Timer? _analysisTimer;

  // Current game state
  GameState? _currentGameState;
  AimPrediction? _currentPrediction;

  // Settings
  bool _showTrajectoryLines = true;
  bool _showPocketHighlights = true;
  bool _showPowerIndicator = true;
  double _analysisFrequency = 10.0; // Hz
  double _predictionAccuracy = 0.8;

  // Getters
  bool get isActive => _isActive;
  bool get isAnalyzing => _isAnalyzing;
  GameState? get currentGameState => _currentGameState;
  AimPrediction? get currentPrediction => _currentPrediction;

  bool get showTrajectoryLines => _showTrajectoryLines;
  bool get showPocketHighlights => _showPocketHighlights;
  bool get showPowerIndicator => _showPowerIndicator;
  double get analysisFrequency => _analysisFrequency;
  double get predictionAccuracy => _predictionAccuracy;

  AimAssistanceService(this._gameIntegrationService);

  /// Starts the aim assistance system
  Future<bool> startAimAssistance() async {
    if (_isActive) return true;

    try {
      // Check permissions first
      final hasPermissions = await _gameIntegrationService.checkPermissions();
      if (!hasPermissions) {
        final granted = await _gameIntegrationService.requestPermissions();
        if (!granted) {
          debugPrint('Required permissions not granted');
          return false;
        }
      }

      // Start screen capture
      final screenCaptureStarted = await _gameIntegrationService.startScreenCapture();
      if (!screenCaptureStarted) {
        debugPrint('Failed to start screen capture');
        return false;
      }

      // Start overlay
      final overlayStarted = await _gameIntegrationService.startOverlay();
      if (!overlayStarted) {
        debugPrint('Failed to start overlay');
        return false;
      }

      // Start analysis loop
      _startAnalysisLoop();

      _isActive = true;
      notifyListeners();

      debugPrint('Aim assistance started successfully');
      return true;

    } catch (e) {
      debugPrint('Error starting aim assistance: $e');
      return false;
    }
  }

  /// Stops the aim assistance system
  Future<bool> stopAimAssistance() async {
    if (!_isActive) return true;

    try {
      // Stop analysis loop
      _stopAnalysisLoop();

      // Stop overlay
      await _gameIntegrationService.stopOverlay();

      // Stop screen capture
      await _gameIntegrationService.stopScreenCapture();

      _isActive = false;
      _currentGameState = null;
      _currentPrediction = null;
      notifyListeners();

      debugPrint('Aim assistance stopped');
      return true;

    } catch (e) {
      debugPrint('Error stopping aim assistance: $e');
      return false;
    }
  }

  /// Updates aim assistance settings
  void updateSettings({
    bool? showTrajectoryLines,
    bool? showPocketHighlights,
    bool? showPowerIndicator,
    double? analysisFrequency,
    double? predictionAccuracy,
  }) {
    bool changed = false;

    if (showTrajectoryLines != null && showTrajectoryLines != _showTrajectoryLines) {
      _showTrajectoryLines = showTrajectoryLines;
      changed = true;
    }

    if (showPocketHighlights != null && showPocketHighlights != _showPocketHighlights) {
      _showPocketHighlights = showPocketHighlights;
      changed = true;
    }

    if (showPowerIndicator != null && showPowerIndicator != _showPowerIndicator) {
      _showPowerIndicator = showPowerIndicator;
      changed = true;
    }

    if (analysisFrequency != null && analysisFrequency != _analysisFrequency) {
      _analysisFrequency = analysisFrequency;
      if (_isActive) {
        _restartAnalysisLoop();
      }
      changed = true;
    }

    if (predictionAccuracy != null && predictionAccuracy != _predictionAccuracy) {
      _predictionAccuracy = predictionAccuracy;
      changed = true;
    }

    if (changed) {
      notifyListeners();
      if (_isActive) {
        _updateOverlayDisplay();
      }
    }
  }

  /// Starts the continuous analysis loop
  void _startAnalysisLoop() {
    _stopAnalysisLoop(); // Ensure no existing timer

    final interval = Duration(milliseconds: (1000 / _analysisFrequency).round());
    _analysisTimer = Timer.periodic(interval, (_) => _performAnalysis());
  }

  /// Stops the analysis loop
  void _stopAnalysisLoop() {
    _analysisTimer?.cancel();
    _analysisTimer = null;
    _isAnalyzing = false;
  }

  /// Restarts the analysis loop with new frequency
  void _restartAnalysisLoop() {
    if (_isActive) {
      _startAnalysisLoop();
    }
  }

  /// Performs a single analysis cycle
  Future<void> _performAnalysis() async {
    if (_isAnalyzing) return; // Skip if already analyzing

    _isAnalyzing = true;

    try {
      // Analyze current screen
      final analysisResult = await _gameIntegrationService.analyzeScreen();
      if (analysisResult != null) {
        // Parse game state
        final gameState = _parseGameState(analysisResult);
        _currentGameState = gameState;

        // Calculate aim prediction
        if (gameState != null && gameState.cueBall != null) {
          final prediction = await _calculateAimPrediction(gameState);
          _currentPrediction = prediction;

          // Update overlay
          if (prediction != null) {
            await _updateOverlay(prediction);
          }
        }

        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error during analysis: $e');
    } finally {
      _isAnalyzing = false;
    }
  }

  /// Parses raw analysis result into game state
  GameState? _parseGameState(Map<String, dynamic> analysisResult) {
    try {
      final tableData = analysisResult['table'] as Map<String, dynamic>?;
      final ballsData = analysisResult['balls'] as List<dynamic>?;
      final pocketsData = analysisResult['pockets'] as List<dynamic>?;

      if (tableData == null || ballsData == null) return null;

      // Parse table
      final table = TableRegion(
        x: (tableData['x'] as num).toDouble(),
        y: (tableData['y'] as num).toDouble(),
        width: (tableData['width'] as num).toDouble(),
        height: (tableData['height'] as num).toDouble(),
      );

      // Parse balls
      final balls = ballsData.map((ballData) {
        final data = ballData as Map<String, dynamic>;
        return Ball(
          x: (data['x'] as num).toDouble(),
          y: (data['y'] as num).toDouble(),
          radius: (data['radius'] as num).toDouble(),
          color: data['color'] as String,
          type: data['type'] as String,
        );
      }).toList();

      // Parse pockets
      final pockets = pocketsData?.map((pocketData) {
        final data = pocketData as Map<String, dynamic>;
        return Pocket(
          x: (data['x'] as num).toDouble(),
          y: (data['y'] as num).toDouble(),
        );
      }).toList() ?? [];

      // Find cue ball and target balls
      final cueBall = balls.firstWhere(
        (ball) => ball.type == 'cue',
        orElse: () => balls.firstWhere((ball) => ball.color == 'white'),
      );

      final targetBalls = balls.where((ball) => ball.type != 'cue' && ball.color != 'white').toList();

      return GameState(
        table: table,
        cueBall: cueBall,
        targetBalls: targetBalls,
        pockets: pockets,
        allBalls: balls,
      );

    } catch (e) {
      debugPrint('Error parsing game state: $e');
      return null;
    }
  }

  /// Calculates aim prediction using physics
  Future<AimPrediction?> _calculateAimPrediction(GameState gameState) async {
    if (gameState.cueBall == null || gameState.targetBalls.isEmpty) return null;

    try {
      // Find best target ball and pocket combination
      AimPrediction? bestPrediction;
      double bestConfidence = 0.0;

      for (final targetBall in gameState.targetBalls) {
        for (final pocket in gameState.pockets) {
          // Calculate shot for this target-pocket combination
          final shotData = {
            'cueBall': {
              'x': gameState.cueBall!.x,
              'y': gameState.cueBall!.y,
              'radius': gameState.cueBall!.radius,
            },
            'targetBall': {
              'x': targetBall.x,
              'y': targetBall.y,
              'radius': targetBall.radius,
            },
            'pocket': {
              'x': pocket.x,
              'y': pocket.y,
            },
            'table': {
              'width': gameState.table.width,
              'height': gameState.table.height,
            },
            'obstacles': gameState.allBalls.where((ball) =>
              ball != gameState.cueBall && ball != targetBall
            ).map((ball) => {
              'x': ball.x,
              'y': ball.y,
              'radius': ball.radius,
            }).toList(),
          };

          // This would call the native physics calculation
          // For now, we'll create a simplified prediction
          final prediction = _createSimplifiedPrediction(gameState, targetBall, pocket);

          if (prediction != null && prediction.confidence > bestConfidence) {
            bestPrediction = prediction;
            bestConfidence = prediction.confidence;
          }
        }
      }

      return bestPrediction;

    } catch (e) {
      debugPrint('Error calculating aim prediction: $e');
      return null;
    }
  }

  /// Creates a simplified prediction for demonstration
  AimPrediction _createSimplifiedPrediction(GameState gameState, Ball targetBall, Pocket pocket) {
    // Simple trajectory from cue ball to target ball to pocket
    final trajectory = [
      Point(gameState.cueBall!.x, gameState.cueBall!.y),
      Point(targetBall.x, targetBall.y),
      Point(pocket.x, pocket.y),
    ];

    // Calculate angle and power
    final dx = targetBall.x - gameState.cueBall!.x;
    final dy = targetBall.y - gameState.cueBall!.y;
    final distance = sqrt(dx * dx + dy * dy);

    return AimPrediction(
      trajectory: trajectory,
      targetBall: targetBall,
      targetPocket: pocket,
      angle: atan2(dy, dx),
      power: (distance / 300).clamp(0.1, 1.0),
      confidence: _predictionAccuracy,
    );
  }

  /// Updates the overlay with current prediction
  Future<void> _updateOverlay(AimPrediction prediction) async {
    if (!_isActive) return;

    final overlayData = {
      'trajectory': prediction.trajectory.map((point) => {
        'x': point.x,
        'y': point.y,
      }).toList(),
      'cueBall': _currentGameState?.cueBall != null ? {
        'x': _currentGameState!.cueBall!.x,
        'y': _currentGameState!.cueBall!.y,
      } : null,
      'targetBall': {
        'x': prediction.targetBall.x,
        'y': prediction.targetBall.y,
      },
      'pocket': {
        'x': prediction.targetPocket.x,
        'y': prediction.targetPocket.y,
      },
      'power': prediction.power,
      'angle': prediction.angle,
      'confidence': prediction.confidence,
      'showTrajectory': _showTrajectoryLines,
      'showPockets': _showPocketHighlights,
      'showPower': _showPowerIndicator,
    };

    await _gameIntegrationService.updateOverlay(overlayData);
  }

  /// Updates overlay display settings
  Future<void> _updateOverlayDisplay() async {
    if (_currentPrediction != null) {
      await _updateOverlay(_currentPrediction!);
    }
  }

  @override
  void dispose() {
    stopAimAssistance();
    super.dispose();
  }
}

// Data classes
class GameState {
  final TableRegion table;
  final Ball? cueBall;
  final List<Ball> targetBalls;
  final List<Pocket> pockets;
  final List<Ball> allBalls;

  GameState({
    required this.table,
    required this.cueBall,
    required this.targetBalls,
    required this.pockets,
    required this.allBalls,
  });
}

class TableRegion {
  final double x, y, width, height;
  TableRegion({required this.x, required this.y, required this.width, required this.height});
}

class Ball {
  final double x, y, radius;
  final String color, type;
  Ball({required this.x, required this.y, required this.radius, required this.color, required this.type});
}

class Pocket {
  final double x, y;
  Pocket({required this.x, required this.y});
}

class Point {
  final double x, y;
  Point(this.x, this.y);
}

class AimPrediction {
  final List<Point> trajectory;
  final Ball targetBall;
  final Pocket targetPocket;
  final double angle, power, confidence;

  AimPrediction({
    required this.trajectory,
    required this.targetBall,
    required this.targetPocket,
    required this.angle,
    required this.power,
    required this.confidence,
  });
}
