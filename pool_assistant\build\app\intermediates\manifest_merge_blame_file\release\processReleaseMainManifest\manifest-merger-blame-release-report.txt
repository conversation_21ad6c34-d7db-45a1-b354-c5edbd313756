1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.poolassistant.pool_assistant"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:5:5-67
12-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:5:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:6:5-79
13-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:6:22-76
14    <uses-permission
14-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:5-107
15        android:name="android.permission.READ_EXTERNAL_STORAGE"
15-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:22-77
16        android:maxSdkVersion="32" />
16-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:78-104
17    <uses-permission
17-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:5-108
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:22-78
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:79-105
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:9:5-76
20-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:9:22-73
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:10:5-78
21-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:10:22-75
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
22-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:11:5-77
22-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:11:22-74
23    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
23-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:12:5-83
23-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:12:22-80
24
25    <!-- Screen capture and overlay permissions -->
26    <uses-permission android:name="android.permission.RECORD_AUDIO" />
26-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:15:5-71
26-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:15:22-68
27    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
27-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:16:5-79
27-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:16:22-76
28    <uses-permission android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT" />
28-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:17:5-86
28-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:17:22-83
29    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />
29-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:18:5-80
29-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:18:22-77
30
31    <!-- Camera permission for computer vision (if needed) -->
32    <uses-permission android:name="android.permission.CAMERA" />
32-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:21:5-65
32-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:21:22-62
33
34    <!-- Hardware features -->
35    <uses-feature
35-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:24:5-85
36        android:name="android.hardware.camera"
36-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:24:19-57
37        android:required="false" />
37-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:24:58-82
38    <uses-feature
38-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:25:5-95
39        android:name="android.hardware.camera.autofocus"
39-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:25:19-67
40        android:required="false" />
40-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:25:68-92
41
42    <!-- For devices with Android 11 (API level 30) and higher -->
43    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
43-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:28:5-29:53
43-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:28:22-74
44    <!--
45         Required to query activities that can process text, see:
46         https://developer.android.com/training/package-visibility and
47         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
48
49         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
50    -->
51    <queries>
51-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:79:5-84:15
52        <intent>
52-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:80:9-83:18
53            <action android:name="android.intent.action.PROCESS_TEXT" />
53-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:81:13-72
53-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:81:21-70
54
55            <data android:mimeType="text/plain" />
55-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:82:13-50
55-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:82:19-48
56        </intent>
57
58        <package android:name="com.facebook.katana" />
58-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:18:9-55
58-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:18:18-52
59    </queries>
60
61    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
61-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:16:5-79
61-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:16:22-76
62
63    <permission
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
64        android:name="com.poolassistant.pool_assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
64-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
65        android:protectionLevel="signature" />
65-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
66
67    <uses-permission android:name="com.poolassistant.pool_assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
68    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
68-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff3ef75755850822f83c0a36c594cb7\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
68-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ff3ef75755850822f83c0a36c594cb7\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
69
70    <application
71        android:name="android.app.Application"
71-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:33:9-42
72        android:allowBackup="true"
72-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:35:9-35
73        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
73-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
74        android:extractNativeLibs="false"
75        android:fullBackupContent="true"
75-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:36:9-41
76        android:icon="@mipmap/ic_launcher"
76-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:34:9-43
77        android:label="Dark Pool"
77-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:32:9-34
78        android:requestLegacyExternalStorage="true"
78-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:37:9-52
79        android:supportsRtl="true" >
79-->[com.facebook.android:facebook-login:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e820d61ce37e44c44b5b21160e7f3c12\transformed\jetified-facebook-login-16.3.0\AndroidManifest.xml:16:18-44
80        <activity
80-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:38:9-59:20
81            android:name="com.poolassistant.pool_assistant.MainActivity"
81-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:39:13-41
82            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
82-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:44:13-163
83            android:exported="true"
83-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:40:13-36
84            android:hardwareAccelerated="true"
84-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:45:13-47
85            android:launchMode="singleTop"
85-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:41:13-43
86            android:taskAffinity=""
86-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:42:13-36
87            android:theme="@style/LaunchTheme"
87-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:43:13-47
88            android:windowSoftInputMode="adjustResize" >
88-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:46:13-55
89
90            <!--
91                 Specifies an Android theme to apply to this Activity as soon as
92                 the Android process has started. This theme is visible to the user
93                 while the Flutter UI initializes. After that, this theme continues
94                 to determine the Window background behind the Flutter UI.
95            -->
96            <meta-data
96-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:51:13-54:17
97                android:name="io.flutter.embedding.android.NormalTheme"
97-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:52:15-70
98                android:resource="@style/NormalTheme" />
98-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:53:15-52
99
100            <intent-filter>
100-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:55:13-58:29
101                <action android:name="android.intent.action.MAIN" />
101-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:56:17-68
101-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:56:25-66
102
103                <category android:name="android.intent.category.LAUNCHER" />
103-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:57:17-76
103-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:57:27-74
104            </intent-filter>
105        </activity>
106
107        <!-- Overlay Service for drawing aim lines -->
108        <service
108-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:62:9-66:63
109            android:name="com.poolassistant.pool_assistant.OverlayService"
109-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:63:13-43
110            android:enabled="true"
110-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:64:13-35
111            android:exported="false"
111-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:65:13-37
112            android:foregroundServiceType="mediaProjection" />
112-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:66:13-60
113
114        <!--
115             Don't delete the meta-data below.
116             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
117        -->
118        <meta-data
118-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:70:9-72:33
119            android:name="flutterEmbedding"
119-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:71:13-44
120            android:value="2" />
120-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:72:13-30
121
122        <service
122-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
123            android:name="androidx.camera.core.impl.MetadataHolderService"
123-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
124            android:enabled="false"
124-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
125            android:exported="false" >
125-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
126            <meta-data
126-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
127                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
127-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
128                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
128-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
129        </service>
130
131        <activity
131-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
132            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
132-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
133            android:exported="false"
133-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
134            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
134-->[:url_launcher_android] C:\Users\<USER>\Desktop\pool\pool_assistant\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
135        <activity
135-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
136            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
136-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
137            android:excludeFromRecents="true"
137-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
138            android:exported="false"
138-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
139            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
139-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
140        <!--
141            Service handling Google Sign-In user revocation. For apps that do not integrate with
142            Google Sign-In, this service will never be started.
143        -->
144        <service
144-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
145            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
145-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
146            android:exported="true"
146-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
147            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
147-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
148            android:visibleToInstantApps="true" />
148-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
149
150        <activity
150-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:22:9-25:66
151            android:name="com.facebook.FacebookActivity"
151-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:23:13-57
152            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
152-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:24:13-96
153            android:theme="@style/com_facebook_activity_theme" />
153-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:25:13-63
154        <activity android:name="com.facebook.CustomTabMainActivity" />
154-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:26:9-71
154-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:26:19-68
155        <activity
155-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:27:9-41:20
156            android:name="com.facebook.CustomTabActivity"
156-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:28:13-58
157            android:exported="true" >
157-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:29:13-36
158            <intent-filter>
158-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:31:13-40:29
159                <action android:name="android.intent.action.VIEW" />
159-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:32:17-69
159-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:32:25-66
160
161                <category android:name="android.intent.category.DEFAULT" />
161-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:34:17-76
161-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:34:27-73
162                <category android:name="android.intent.category.BROWSABLE" />
162-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:35:17-78
162-->[com.facebook.android:facebook-common:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7878a769bcd0a33f7616706e06821eb2\transformed\jetified-facebook-common-16.3.0\AndroidManifest.xml:35:27-75
163
164                <data
164-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:82:13-50
165                    android:host="cct.com.poolassistant.pool_assistant"
166                    android:scheme="fbconnect" />
167            </intent-filter>
168        </activity>
169        <activity
169-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
170            android:name="com.google.android.gms.common.api.GoogleApiActivity"
170-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
171            android:exported="false"
171-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
172            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
172-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4003e89e11d20e7aac223715520d9d9\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
173
174        <meta-data
174-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21b502c8435235a3b0ddf73178b4adb9\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
175            android:name="com.google.android.gms.version"
175-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21b502c8435235a3b0ddf73178b4adb9\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
176            android:value="@integer/google_play_services_version" />
176-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21b502c8435235a3b0ddf73178b4adb9\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
177        <!--
178         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
179         with the application context. This config is merged in with the host app's manifest,
180         but there can only be one provider with the same authority activated at any given
181         point; so if the end user has two or more different apps that use Facebook SDK, only the
182         first one will be able to use the provider. To work around this problem, we use the
183         following placeholder in the authority to identify each host application as if it was
184         a completely different provider.
185        -->
186        <provider
186-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:29:9-32:40
187            android:name="com.facebook.internal.FacebookInitProvider"
187-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:30:13-70
188            android:authorities="com.poolassistant.pool_assistant.FacebookInitProvider"
188-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:31:13-72
189            android:exported="false" />
189-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:32:13-37
190
191        <receiver
191-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:34:9-40:20
192            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
192-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:35:13-86
193            android:exported="false" >
193-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:36:13-37
194            <intent-filter>
194-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:37:13-39:29
195                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
195-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:38:17-95
195-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:38:25-92
196            </intent-filter>
197        </receiver>
198        <receiver
198-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:41:9-47:20
199            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
199-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:42:13-118
200            android:exported="false" >
200-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:43:13-37
201            <intent-filter>
201-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:44:13-46:29
202                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
202-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:45:17-103
202-->[com.facebook.android:facebook-core:16.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bce28d73a095cc558efd70e46cccf5d\transformed\jetified-facebook-core-16.3.0\AndroidManifest.xml:45:25-100
203            </intent-filter>
204        </receiver>
205
206        <provider
206-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
207            android:name="androidx.startup.InitializationProvider"
207-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
208            android:authorities="com.poolassistant.pool_assistant.androidx-startup"
208-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
209            android:exported="false" >
209-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
210            <meta-data
210-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
211-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
212                android:value="androidx.startup" />
212-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
213            <meta-data
213-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
214                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
214-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
215                android:value="androidx.startup" />
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
216        </provider>
217
218        <uses-library
218-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
219            android:name="androidx.window.extensions"
219-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
220            android:required="false" />
220-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
221        <uses-library
221-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
222            android:name="androidx.window.sidecar"
222-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
223            android:required="false" />
223-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
224
225        <receiver
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
226            android:name="androidx.profileinstaller.ProfileInstallReceiver"
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
227            android:directBootAware="false"
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
228            android:enabled="true"
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
229            android:exported="true"
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
230            android:permission="android.permission.DUMP" >
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
231            <intent-filter>
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
232                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
233            </intent-filter>
234            <intent-filter>
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
235                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
236            </intent-filter>
237            <intent-filter>
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
238                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
239            </intent-filter>
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
241                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
242            </intent-filter>
243        </receiver>
244    </application>
245
246</manifest>
