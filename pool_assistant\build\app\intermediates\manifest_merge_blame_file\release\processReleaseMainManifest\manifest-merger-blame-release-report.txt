1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.poolassistant.pool_assistant"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:5:5-67
12-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:5:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:6:5-79
13-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:6:22-76
14    <uses-permission
14-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:5-107
15        android:name="android.permission.READ_EXTERNAL_STORAGE"
15-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:22-77
16        android:maxSdkVersion="32" />
16-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:7:78-104
17    <uses-permission
17-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:5-108
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:22-78
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:8:79-105
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:9:5-76
20-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:9:22-73
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:10:5-78
21-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:10:22-75
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
22-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:11:5-77
22-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:11:22-74
23    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
23-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:12:5-83
23-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:12:22-80
24
25    <!-- Screen capture and overlay permissions -->
26    <uses-permission android:name="android.permission.RECORD_AUDIO" />
26-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:15:5-71
26-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:15:22-68
27    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
27-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:16:5-79
27-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:16:22-76
28    <uses-permission android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT" />
28-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:17:5-86
28-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:17:22-83
29    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />
29-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:18:5-80
29-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:18:22-77
30
31    <!-- Camera permission for computer vision (if needed) -->
32    <uses-permission android:name="android.permission.CAMERA" />
32-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:21:5-65
32-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:21:22-62
33
34    <!-- Hardware features -->
35    <uses-feature
35-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:24:5-85
36        android:name="android.hardware.camera"
36-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:24:19-57
37        android:required="false" />
37-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:24:58-82
38    <uses-feature
38-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:25:5-95
39        android:name="android.hardware.camera.autofocus"
39-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:25:19-67
40        android:required="false" />
40-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:25:68-92
41
42    <!-- For devices with Android 11 (API level 30) and higher -->
43    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
43-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:28:5-29:53
43-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:28:22-74
44    <!--
45         Required to query activities that can process text, see:
46         https://developer.android.com/training/package-visibility and
47         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
48
49         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
50    -->
51    <queries>
51-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:79:5-84:15
52        <intent>
52-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:80:9-83:18
53            <action android:name="android.intent.action.PROCESS_TEXT" />
53-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:81:13-72
53-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:81:21-70
54
55            <data android:mimeType="text/plain" />
55-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:82:13-50
55-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:82:19-48
56        </intent>
57    </queries>
58
59    <permission
59-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
60        android:name="com.poolassistant.pool_assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
60-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
61        android:protectionLevel="signature" />
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
62
63    <uses-permission android:name="com.poolassistant.pool_assistant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
64
65    <application
66        android:name="android.app.Application"
66-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:33:9-42
67        android:allowBackup="true"
67-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:35:9-35
68        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
68-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
69        android:extractNativeLibs="false"
70        android:fullBackupContent="true"
70-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:36:9-41
71        android:icon="@mipmap/ic_launcher"
71-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:34:9-43
72        android:label="Dark Pool"
72-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:32:9-34
73        android:requestLegacyExternalStorage="true" >
73-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:37:9-52
74        <activity
74-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:38:9-59:20
75            android:name="com.poolassistant.pool_assistant.MainActivity"
75-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:39:13-41
76            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
76-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:44:13-163
77            android:exported="true"
77-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:40:13-36
78            android:hardwareAccelerated="true"
78-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:45:13-47
79            android:launchMode="singleTop"
79-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:41:13-43
80            android:taskAffinity=""
80-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:42:13-36
81            android:theme="@style/LaunchTheme"
81-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:43:13-47
82            android:windowSoftInputMode="adjustResize" >
82-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:46:13-55
83
84            <!--
85                 Specifies an Android theme to apply to this Activity as soon as
86                 the Android process has started. This theme is visible to the user
87                 while the Flutter UI initializes. After that, this theme continues
88                 to determine the Window background behind the Flutter UI.
89            -->
90            <meta-data
90-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:51:13-54:17
91                android:name="io.flutter.embedding.android.NormalTheme"
91-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:52:15-70
92                android:resource="@style/NormalTheme" />
92-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:53:15-52
93
94            <intent-filter>
94-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:55:13-58:29
95                <action android:name="android.intent.action.MAIN" />
95-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:56:17-68
95-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:56:25-66
96
97                <category android:name="android.intent.category.LAUNCHER" />
97-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:57:17-76
97-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:57:27-74
98            </intent-filter>
99        </activity>
100
101        <!-- Overlay Service for drawing aim lines -->
102        <service
102-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:62:9-66:63
103            android:name="com.poolassistant.pool_assistant.OverlayService"
103-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:63:13-43
104            android:enabled="true"
104-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:64:13-35
105            android:exported="false"
105-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:65:13-37
106            android:foregroundServiceType="mediaProjection" />
106-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:66:13-60
107
108        <!--
109             Don't delete the meta-data below.
110             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
111        -->
112        <meta-data
112-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:70:9-72:33
113            android:name="flutterEmbedding"
113-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:71:13-44
114            android:value="2" />
114-->C:\Users\<USER>\Desktop\pool\pool_assistant\android\app\src\main\AndroidManifest.xml:72:13-30
115
116        <service
116-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
117            android:name="androidx.camera.core.impl.MetadataHolderService"
117-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
118            android:enabled="false"
118-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
119            android:exported="false" >
119-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
120            <meta-data
120-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
121                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
121-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
122                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
122-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c091dce789e802e9021e6fe2a76e431a\transformed\jetified-camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
123        </service>
124
125        <provider
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
126            android:name="androidx.startup.InitializationProvider"
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
127            android:authorities="com.poolassistant.pool_assistant.androidx-startup"
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
128            android:exported="false" >
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
129            <meta-data
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
131                android:value="androidx.startup" />
131-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
134                android:value="androidx.startup" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
135        </provider>
136
137        <uses-library
137-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
138            android:name="androidx.window.extensions"
138-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
139            android:required="false" />
139-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
140        <uses-library
140-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
141            android:name="androidx.window.sidecar"
141-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
142            android:required="false" />
142-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
143
144        <receiver
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
145            android:name="androidx.profileinstaller.ProfileInstallReceiver"
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
146            android:directBootAware="false"
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
147            android:enabled="true"
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
148            android:exported="true"
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
149            android:permission="android.permission.DUMP" >
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
150            <intent-filter>
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
151                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
152            </intent-filter>
153            <intent-filter>
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
154                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
155            </intent-filter>
156            <intent-filter>
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
157                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
158            </intent-filter>
159            <intent-filter>
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
160                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
161            </intent-filter>
162        </receiver>
163    </application>
164
165</manifest>
